{% include 'header.html' %}
<div class="container">
    {% if session['username'] == null %}
        <h1>欢迎使用运维助手，请先登录！</h1>
    {% else %}
        <h3>字符串处理</h3>
        <form action="/tools_handle_str" method="post">
            <div class="form-group">
                <label for="original_text">待处理的字符串：</label>
                <textarea class="form-control" rows="5" id="original_text"
                          name="original_text">{{ original_text }}</textarea>
            </div>
            <input type="submit" class="btn btn-danger" name="bs4encode" value="Base64编码"></input>
            <input type="submit" class="btn btn-danger" name="bs4decode" value="Base64解码"></input>
            <input type="submit" class="btn btn-primary" name="urlencode" value="URL编码"></input>
            <input type="submit" class="btn btn-primary" name="urldecode" value="URL解码"></input>
            <input type="submit" class="btn btn-success" name="str2ascii" value="字符转ASCII"></input>
            <input type="submit" class="btn btn-success" name="ascii2str" value="ASCII转字符"></input>
            <input type="submit" class="btn btn-info" name="jsonformat" value="JSON格式化"></input>
            <input type="submit" class="btn btn-info" name="md5" value="MD5加密"></input>
            <input type="submit" class="btn btn-warning" name="translate" value="中英互译"></input>
            <input type="submit" class="btn btn-secondary" name="time_timestamp" value="时间戳转换"></input>
        </form>
        <div class="form-group">
            <label for="result_text">处理后的结果：</label>
            <textarea class="form-control" rows="5" id="result_text" name="result_text">{{ result_text }}</textarea>
        </div>
        <button class="btn btn-primary" type="button" name="copy_text" value="复制"
                onclick="copy_text('result_text')">复制结果
        </button>
        </div>
    {% endif %}
</div>
{% include 'footer.html' %}