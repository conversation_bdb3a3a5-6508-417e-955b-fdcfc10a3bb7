1	标准的FTP文件路径	-
2	标准的FTP文件夹路径	-
3	标准的LAN文件路径	-
4	标准的LAN文件夹路径	-
5	标准的HTTP文件路径	-
6	不存在	-
7	标准的DB文件路径	-
8	不存在	-
9	标准的LOCAL文件路径	-
10	标准的LOCAL文件夹路径	-
11	FTP文件夹路径	路径下有文件imageindex.txt记录了当前文件夹下所有的文件信息
12	LAN文件路径	需要将每个文件分类到一个序列中，序列的UID按照文件名（不带后缀）命名
13	标准的OSS文件路径	-
14	标准的OSS文件夹路径	-
15	HTTP文件夹路径	原始路径是HTTP前缀拼接序列信息XML，需要解析该XML才能得到序列的文件信息
16	LAN文件夹路径	路径下有特殊的文件夹路径
17	FTP文件夹路径	检查的所有文件全部存在一个文件夹路径中，但是数据库无法取得序列信息
18	新IC文件路径	检查按照序列带密码7Z压缩
19	标准的OOS文件路径	-
20	标准的OOS文件夹路径	-
21	HTTP文件夹路径	需要对特定的链接进行GET请求后得到检查的序列信息，再通过HTTP下载文件
22	LAN文件夹路径	检查下的所有文件按照正常的存储格式存放，但是数据库无法取得序列信息
23	LAN文件路径	序列号带"0_"或者"_keyframe"这种特殊字符的是普通的文件路径，其余的均走普通的文件夹路径
24	LAN文件夹路径	下载方式同22，但是序列UID的命名需要取原始DICOM中的DCM_SeriesNumber，而不能通过文件夹的名字命名
25	RAR压缩包路径	压缩包存在标准的LAN路径中，需要解压并且按照原始DICOM图中的DCM_SeriesInstanceUID进行序列分类
26	LAN文件夹路径	检查的所有文件全部存在一个文件夹路径中，但是数据库无法取得序列信息
27	HTTP文件夹路径	下载方式按照标准的HTTP文件路径，但是序列UID的命名需要取原始DICOM中的DCM_SeriesNumber
28	PK类型文件路径	下载方式按照GY_HTTP_FOLDER下载，但是由于文件是PK压缩的，并且可能又是JPEG2000压缩的，实时解压会很慢，所以下载到本地的文件需要经过PK解压和JPEG2000解压后直接存放在本地，并且不删除
29	LAN文件路径	它的关键帧在一个dcm文件里面记录着，并且指向这个检查某个序列的一个文件