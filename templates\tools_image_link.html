{% include 'header.html' %}
<style>
    h3 {
        text-align: center;
        margin: 20px 0;
    }

    .btn {
        background-color: #4279F5;
        color: white;
    }

    textarea {
        margin: 20px 0 10px 0;
        padding-top: 20px;
        height: 150px !important;
    }

    div.copy {
        text-align: right !important;
    }
</style>
{#<script>#}
{#    function copy_link(id) {#}
{#        value = document.getElementById(id).innerHTML#}
{#        navigator.clipboard.writeText(value).then(() => {#}
{#            alert('链接复制成功！');#}
{#        });#}
{#    }#}
{#</script>#}
<div class="container">
    {% if session['username'] == null %}
        <h1>欢迎使用运维助手，请先登录！</h1>
    {% else %}
        <h3>云影像链接生成</h3>
        <form action="/tools_image_link" method="post" class="input-group">
            <div class="input-group-btn">
                <select class="form-control" class="btn btn-default dropdown-toggle" name="hospital"
                        data-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false" style="width: auto">
                    <option value="zz" name="hospital">中山大学附属肿瘤医院</option>
                    <option value="sy" name="hospital">广东省人民医院</option>
                    <option value="ze" name="hospital">中山大学孙逸仙纪念医院</option>
                    <option value="cm" name="hospital">北京慈铭体检中心</option>
                    <option value="yz" name="hospital">云南省肿瘤医院</option>

                </select>
            </div>
            <input type="text" class="form-control" name="patcard" id="patcard"
                   placeholder="中肿：输入'姓名,病历号'  /  其它：输入'患者号'">
            <span class="input-group-btn">
            <button class="btn btn-default" type="submit" name="submit" value="submit">查询</button>
        </span>
        </form>
        <textarea id="text"
                  class="form-control col-lg-12 col-md-12 col-xs-12 col-sm-12">{{ cloudImgLink }}</textarea>
        <div class="copy">
            <button class="btn btn-default" type="submit" name="copy" value="copy" onclick="copy_text('text')">复制
            </button>
        </div>
    {% endif %}
</div>
{% include 'footer.html' %}