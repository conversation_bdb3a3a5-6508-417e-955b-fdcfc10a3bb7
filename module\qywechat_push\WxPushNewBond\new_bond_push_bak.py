import datetime
import json
import time
import requests
from bs4 import BeautifulSoup
from PIL import Image, ImageDraw, ImageFont
import traceback

'''
本文件主要实现通过企业微信应用给企业成员发消息
1、IP加到白名单：应用-企业可信IP中
2、微信接收通知：我的企业-微信插件-邀请关注
3、获取每日新债情况，进行推送
4、推送到微信
'''


# 每日新债推送
class NewBondPush:
    def __init__(self):
        # 企业微信api
        self.CORP_ID = "ww88d75674bd4d4447"  # 我的企业中查看企业ID
        # 应用中查看Secret，每日简报-1000003
        self.SECRET = 'odKlc2MjaH4Gkf3PtqJoXlOH6KiUiw2wBPaWl0lQA_c'
        self.session = requests.session()

    # 获取今日新债数据
    def get_today_bonds(self):
        try:
            request_params = {
                "headers": {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.97 Safari/537.36"
                },
                "timeout": 5
            }
            r = requests.get("http://data.hexin.cn/ipo/bond/cate/info/", **request_params)
            # print(json.dumps(r.json(),ensure_ascii=False))
            bonds = json.loads(r.text)
            text = []
            for bond in bonds:
                # today = time.strftime('%Y-%m-%d', time.localtime(time.time()))
                today = datetime.date.today().strftime('%Y-%m-%d')
                if today == bond.get('sgDate'):
                    text.append(f"""今日打新: {bond.get('zqName')} 发行量：{bond.get('issue')}亿""")
            # print('\n'.join(text))
            if text:
                return '\n'.join(text)
            else:
                return None
        except Exception as e:
            return e

    # 企业微信-获取token
    def get_token(self):
        url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={self.CORP_ID}&corpsecret={self.SECRET}"
        rep = self.session.get(url)
        if rep.status_code != 200:
            print('token获取失败')
            return None
        token = json.loads(rep.content)['access_token']
        print('token获取成功')
        return token

    # 企业微信-消息推送
    def send_msg(self, content):
        try:
            # 文本消息数据
            form_data = {
                "touser": "LiZhaoMing",
                "toparty": "1",
                "totag": ["tagid1 | tagid1"],
                "toall": 0,
                "msgtype": "textcard",
                "agentid": 1000002,
                "textcard": {
                    "title": "今日有新债，请申购",
                    "description": content,
                    "url": "http://data.hexin.cn/ipo/xgsgzq/",
                    "btntxt": "更多"
                },
                "safe": 0
            }
            token = self.get_token()
            url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + token
            header = {
                "Content-Type": "application/json"
            }
            # 打印消息推送内容
            # print(form_data)
            r = self.session.post(url, data=json.dumps(form_data, ensure_ascii=False).encode('utf-8'),
                                  headers=header)
            if r.status_code != 200:
                print("发送请求失败！")
            # 打印消息推送后返回的信息
            rep = json.loads(r.content)
            print(f'消息发送成功...', rep)
        except Exception as e:
            print(e)

    # 企业微信-设置定时推送
    def push_msg(self):
        set_time = '08:50'  # 自定义提醒时间
        while True:
            current_time = time.strftime('%H:%M', time.localtime(time.time()))  # 获取为HH:MM时间格式
            if set_time != current_time:
                print(f'到达设定时间:{current_time}\n准备消息发送...')
                # pygame.mixer.init()
                # 每日新债-数据获取
                text = self.get_today_bonds()
                self.send_msg(text)
                time.sleep(60)


if __name__ == "__main__":
    # 企业微信消息推送
    newBondPush = NewBondPush()
    newBondPush.push_msg()
