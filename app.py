import openai
from flask import Flask, render_template, redirect, url_for, request, session, Response
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, current_user, login_required, UserMixin  # 引入用户基类
from flask_wtf import FlaskForm
from werkzeug.security import generate_password_hash, check_password_hash
from wtforms import String<PERSON>ield, PasswordField
from wtforms.validators import DataRequired
import pandas as pd
import requests, json, os, time, zlib, uuid
import xml.etree.ElementTree as ET
from websocket import create_connection  # 先安装websocket，再安装websocket-client
from xpinyin import Pinyin
from module.do_version_manage import MyVersionManage
from module.do_mysql_connect import MyConnectMySQL
from module.do_imagecloud_link import MyHandleRSA, MyPdfHandleRSA
from module.do_tools import MyTools
from module.do_del_consultation import ConsultationManager

app = Flask(__name__)  # 创建 Flask 应用

app.secret_key = 'abc'  # 设置表单交互密钥

login_manager = LoginManager()  # 实例化登录管理对象
login_manager.init_app(app)  # 初始化应用
login_manager.login_view = 'login'  # 设置用户登录视图函数 endpoint

USERS = [
    {
        "id": 1,
        "name": 'lizhaoming',
        "password": generate_password_hash('annet123')
    },
    {
        "id": 2,
        "name": 'gerenxin',
        "password": generate_password_hash('annet123')
    },
    {
        "id": 3,
        "name": 'gongjinsi',
        "password": generate_password_hash('annet123')
    },
    {
        "id": 4,
        "name": 'xielin',
        "password": generate_password_hash('annet123')
    },
    {
        "id": 5,
        "name": 'gujiarong',
        "password": generate_password_hash('annet123')
    },
    {
        "id": 6,
        "name": 'wanxingxing',
        "password": generate_password_hash('annet123')
    }
]


# 创建一个用户
def create_user(user_name, password):
    user = {
        "name": user_name,
        "password": generate_password_hash(password),
        "id": uuid.uuid4()
    }
    USERS.append(user)


# 根据用户名获得用户记录
def get_user(user_name):
    for user in USERS:
        if user.get("name") == user_name:
            return user
    return None


# 创建用户类
class User(UserMixin):
    """用户类"""

    def __init__(self, user):
        self.username = user.get("name")
        self.password_hash = user.get("password")
        self.id = user.get("id")

    # 密码验证
    def verify_password(self, password):
        if self.password_hash is None:
            return False
        return check_password_hash(self.password_hash, password)

    # 获取用户ID
    def get_id(self):
        return self.id

    # 根据用户ID获取用户实体，为login_user方法提供支持
    @staticmethod
    def get(user_id):
        if not user_id:
            return None
        for user in USERS:
            if user.get('id') == user_id:
                return User(user)
        return None


# 定义获取登录用户的方法
@login_manager.user_loader
def load_user(user_id):
    return User.get(user_id)


# 登录表单类
class LoginForm(FlaskForm):
    username = StringField('用户名', validators=[DataRequired()])
    password = PasswordField('密码', validators=[DataRequired()])


# 登录
@app.route('/login/', methods=('GET', 'POST'))
def login():
    emsg = None
    if request.method == 'POST':
        user_name = request.form.get('username')
        password = request.form.get('password')
        user_info = get_user(user_name)  # 从用户数据中查找用户记录
        if user_info is None:
            emsg = "用户名或密码有误，请重新输入..."
        else:
            user = User(user_info)  # 创建用户实体
            if user.verify_password(password):  # 校验密码
                login_user(user)  # 创建用户 Session
                session['username'] = user_name

                # 获取登录用户的信息，存入登录记录表
                df = pd.read_excel('./static/excel/login_record.xlsx')
                current_time = time.strftime('%Y-%m-%d %H:%M:%S')
                # new_row = pd.DataFrame({
                #     '用户': [username],
                #     '访问时间': [current_time]
                # })
                # df_all = pd.concat([new_row, df])
                # df_all.to_excel('./static/excel/login_record.xlsx', index=False)
                # print(request.environ.get('HTTP_X_FORWARDED_FOR'))
                try:
                    if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
                        ip = request.environ['REMOTE_ADDR']
                    else:
                        ip = request.environ['HTTP_X_FORWARDED_FOR']  # if behind a proxy
                    r = requests.get(f'http://ip-api.com/json/{ip}', timeout=5)
                    # r = requests.get('http://ip-api.com/json/*************', timeout=5)
                    # print(ip, r.status_code)
                    info = json.loads(r.text)
                    city, isp = info['city'], info['isp']
                    print(user_name, current_time, ip, city, isp)
                    new_row = pd.DataFrame({
                        '用户': [user_name],
                        'IP': [ip],
                        '运营商': [isp],
                        '城市': [city],
                        '访问时间': [current_time]
                    })
                    df_all = pd.concat([new_row, df])
                    df_all.to_excel('./static/excel/login_record.xlsx', index=False)
                except Exception as e:
                    print(e)
                return redirect(request.args.get('next') or url_for('index'))
            else:
                emsg = "用户名或密码有误，请重新输入..."
    return render_template('login.html', emsg=emsg)


# 首页
@app.route('/')
@login_required  # 需要登录才能访问
def index():
    df = pd.read_excel('./static/excel/login_record.xlsx')
    return render_template('index.html', username=current_user.username,
                           table_html=df.to_html(classes='table table-striped table-hover table-bordered'))


# 登出
@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))


def query_data(sql):
    """
    执行数据库查询。

    :param sql: 要执行的SQL查询语句。
    :return: 查询结果。
    """
    return MyConnectMySQL().quary_data(sql)


def insert_or_update_table(sql):
    """
    执行数据库插入或更新操作。

    :param sql: 要执行的SQL插入或更新语句。
    """
    MyConnectMySQL().insert_or_update_table(sql)


# 查询表单
@app.route('/master_query_form')
def master_query_form():
    return render_template('master_query_form.html')


# 查询验证码
@app.route('/get_phone_code', methods=['POST'])
def get_phone_code():
    phone_code = request.form.get('phone_code')
    if len(phone_code) != 0:
        sql = f'''
            SELECT DISTINCT
                security_code,
                phone,
                FROM_UNIXTIME(LEFT(create_time, 10)) create_time,
                FROM_UNIXTIME(LEFT(expire_time, 10)) expire_time 
            FROM user_phone_code WHERE phone='{phone_code}'
            '''
        user_phone_code = query_data(sql)
        return render_template('master_query_result.html', user_phone_code=user_phone_code)
    else:
        return render_template('master_query_form.html')


# 查询用户信息
@app.route('/get_user_info', methods=['POST'])
def get_user_info():
    phone_info = request.form.get('phone_info')
    if len(phone_info) != 0:
        sql = f'''
            SELECT DISTINCT
                a.name,
                a.role,
                a.userid,
                a.phone,
                b.password,
                FROM_UNIXTIME(LEFT(b.modify_password_time, 10)) modify_password_time,
                b.fail_num  
            FROM user_baseinfo a
            LEFT JOIN user b ON a.userid = b.userid
            WHERE a.phone='{phone_info}'
            '''
        user_baseinfo = query_data(sql)
        return render_template('master_query_result.html', user_baseinfo=user_baseinfo)
    else:
        return render_template('master_query_form.html')


# 查询关联信息
@app.route('/get_user_bind_info', methods=['POST'])
def get_user_bind_info():
    phone_band = request.form.get('phone_band')
    if len(phone_band) != 0:
        sql = f'''
            SELECT DISTINCT
                dataaccount,
                name,
                orgname,
                deptname,
                office,
                userid,
                phone
            FROM user_dataaccount WHERE dataaccount='{phone_band}' or phone='{phone_band}'
            '''
        user_dataaccount = query_data(sql)
        return render_template('master_query_result.html', user_dataaccount=user_dataaccount)
    else:
        return render_template('master_query_form.html')


# 查询账号操作记录
@app.route('/get_operation_record', methods=['POST'])
def get_operation_record():
    data_account = request.form.get('data_account')
    if len(data_account) != 0:
        sql = f'''
            SELECT DISTINCT
                create_date,
                data_account,
                user_id,
                operation,
                org_code,
                data_token
            FROM tbl_data_account_operation 
            WHERE data_account='{data_account}' or user_id='{data_account}'
            ORDER BY create_date DESC
            '''
        operation_record = query_data(sql)
        return render_template('master_query_result.html', operation_record=operation_record)
    else:
        return render_template('master_query_form.html')


# 查询医院信息
@app.route('/get_hospital_info', methods=['POST'])
def get_hospital_info():
    org_name = request.form.get('org_name')
    if len(org_name) != 0:
        sql = f'''
            SELECT DISTINCT
                b.org_name,
                b.org_code,
                a.cds_ip,
                a.cds_port,
                a.cds_web_socket_port,
                a.cds_version,
                a.is_watermark,
                a.water_mark_config,
                b.logo_url,
                b.create_time
            FROM tbl_organization_config  a LEFT JOIN tbl_organization b ON a.org_code = b.org_code
            WHERE b.org_name LIKE '%{org_name}%'
            '''
        organization_config = query_data(sql)
        print(organization_config)
        return render_template('master_query_result.html', organization_config=organization_config)
    else:
        return render_template('master_query_form.html')


# 解锁账号
@app.route('/unlock_login', methods=['POST'])
def unlock_login():
    lock_phone = request.form.get('lock_phone')
    if len(lock_phone) != 0:
        sql = f'''
            UPDATE user a LEFT JOIN user_baseinfo b ON a.userid = b.userid
            SET a.fail_num='0' WHERE b.phone='{lock_phone}'
            '''
        insert_or_update_table(sql)
        return '账号解锁成功！'
    else:
        return render_template('master_query_form.html')


# 删除设备登录记录
@app.route('/del_login_record', methods=['POST'])
def del_login_record():
    user_id = request.form.get('user_id')
    if len(user_id) != 0:
        sql = f'''
            DELETE FROM tbl_login_record WHERE user_id='{user_id}'
            '''
        insert_or_update_table(sql)
        return '设备登录记录删除成功！'
    else:
        return render_template('master_query_form.html')


# 删除会诊记录
@app.route('/del_consultation_record', methods=['POST'])
def del_consultation_record():
    user_id = request.form.get('conn_user_id')
    consultation_manager = ConsultationManager()  # 实例化会诊管理类
    ids = consultation_manager.get_consultation_groupid(user_id, 200)  # 查询会诊中心前多少条会诊群组ID
    if ids:
        for id in ids[:-10]:
            consultation_id = id.get('consultationId')
            session_id = id.get('sessionId')
            consultation_manager.del_table_conn_record(consultation_id)  # 删除会诊表记录
            consultation_manager.destroy_group(session_id)  # 解散群组
        return '会诊记录删除成功！'
    else:
        return render_template('master_query_form.html')


# 2 版本维护
def generate_qr_code(version_name, download_url):
    """
    生成下载二维码。

    :param version_name: 版本名称。
    :param download_url: 下载链接。
    """
    logo_path = f'../static/app/base_imgs/{version_name}-logo.png'
    qr_code_path = f'./static/app/version_imgs/{version_name}.png'

    if not os.path.exists(qr_code_path):
        try:
            my_version_manager = MyVersionManage()
            my_version_manager.download_base_img(download_url)
            time.sleep(10)
            my_version_manager.img_synthesis(version_name)
            my_version_manager.img_synthesis_new(version_name)
            print(f'{version_name},二维码生成成功')
        except Exception as e:
            print(f'{version_name},二维码生成失败，失败原因：{e}')

    return qr_code_path, logo_path


@app.route('/app_versions', methods=['GET', 'POST'])
def get_version_list():
    input_name = request.form.get('version_name')
    my_version_manager = MyVersionManage()
    version_list = my_version_manager.get_version_group_list()
    result = []

    for data in version_list:
        version_name = data['name']
        version_desc = data.get('description')
        all_counts = int(data.get('all_counts', 0))
        ios_counts = data.get('ios_counts')
        android_counts = data.get('android_counts')
        download_url = data['dowmload_url']

        qr_code_path, logo_path = generate_qr_code(version_name, download_url)

        result.append({
            'version_name': version_name,
            'version_desc': version_desc,
            'all_counts': all_counts,
            'ios_counts': ios_counts,
            'android_counts': android_counts,
            'version_logo': logo_path,
            'download_url': download_url,
            'qr_code_path': qr_code_path
        })

    result.sort(key=lambda s: s['all_counts'], reverse=True)

    return render_template('app_versions.html', result=result)


# 3 产品注册（cds/大屏/引擎）
def generate_registration_code(mac_code):
    """
    生成注册码。

    :param mac_code: MAC 地址。
    :return: 生成的注册码。
    """
    my_tools = MyTools()
    return my_tools.register_cds(mac_code)


def save_registration_record(reg_info, mac_code, reg_code):
    """
    保存注册记录到 Excel 文件。

    :param reg_info: 注册信息。
    :param mac_code: MAC 地址。
    :param reg_code: 注册码。
    """
    df = pd.read_excel('./static/excel/reg_record.xlsx')
    current_time = time.strftime('%Y-%m-%d %H:%M:%S')
    register, city, hospital, product = reg_info.strip().split('-')
    new_row = pd.DataFrame({
        '注册人': [register],
        '注册日期': [current_time],
        '注册城市': [city],
        '项目名称': [hospital],
        '注册程序': [product],
        '注册码': [reg_code]
    })
    df_all = pd.concat([new_row, df])
    df_all.to_excel('./static/excel/reg_record.xlsx', index=False)


@app.route('/product_reg', methods=['GET', 'POST'])
def get_product_reg():
    reg_info = ''
    mac_code = ''
    reg_code = ''

    if request.method == 'POST':
        reg_info = request.form.get('reg_info')
        mac_code = request.form.get('mac_code')

        # 生成注册码
        reg_code = generate_registration_code(mac_code)

        if reg_info and mac_code:
            # 保存注册记录
            save_registration_record(reg_info, mac_code, reg_code)
            print(reg_info, mac_code, reg_code)

        return render_template('product_reg.html', reg_info=reg_info, mac_code=mac_code, reg_code=reg_code)

    return render_template('product_reg.html', reg_info=reg_info, mac_code=mac_code, reg_code=reg_code)


# 4 添加演示账号
def generate_data_account_name(name):
    """
    生成数据账号名称，使用拼音。

    :param name: 姓名。
    :return: 生成的数据账号名称。
    """
    p = Pinyin()
    return p.get_pinyin(name, '')


def get_api_url(orgname):
    """
    获取医院对应的 API URL。

    :param orgname: 医院名称。
    :return: 医院对应的 API URL。
    """
    api_urls = {
        '安泰云医院': 'http://120.25.148.207:9595/api/user/updateUser',
        '安友云医院': 'http://120.79.31.192:9811/api/user/updateUser',
        '亚新云医院': 'http://120.79.31.192:9824/api/user/updateUser',
    }
    return api_urls.get(orgname, '')


@app.route('/add_dataaccount', methods=['GET', 'POST'])
def add_dataaccount():
    if request.method == 'GET':
        return render_template('add_dataaccount.html')

    if request.method == 'POST':
        orgname = request.form.get('orgname')
        deptname = request.form.get('deptname')
        name = request.form.get('name')

        # 生成数据账号名称
        dataaccount = generate_data_account_name(name)
        code = ''

        if orgname != '--请选择医院--' and deptname != '--请选择所属科室--' and name:
            # 获取医院对应的 API URL
            url = get_api_url(orgname)

            headers = {
                "Content-Type": "application/json;charset=UTF-8"
            }

            data = {
                "user": {
                    "username": dataaccount,
                    "password": "E10ADC3949BA59ABBE56E057F20F883E",
                    "name": name,
                    "role": "4",
                    "gender": "1",
                    "age": "1",
                    "identificationNumber": "1",
                    "goodAt": "1",
                    "physicianLicence": "1",
                    "staffId": "1",
                    "office": "4011",
                    "officeName": deptname,
                    "orgCode": "JCYY"
                },
                "inDepartAuth": [
                    "4010"
                ],
                "outDepartAuth": [],
                "resourceAuth": [],
                "modalityAuth": [],
                "type": "ADD"
            }

            # 发送 POST 请求
            r = requests.post(url, headers=headers, data=json.dumps(data))
            rep = json.loads(r.text)
            code = rep.get('code')
            error = rep.get('error')

            if r.status_code == 200:
                result = f'关联医院：{orgname}，账号：{dataaccount}，密码：123456，姓名：{name}'
                print(result)
                return render_template('add_dataaccount.html', code=code, error=error, result=result)
            else:
                return render_template('add_dataaccount.html')
        else:
            return render_template('add_dataaccount.html')

    return render_template('add_dataaccount.html')


# 工具箱
# 云影像链接生成
@app.route('/tools_image_link', methods=['GET', 'POST'])
def get_cloud_image_link():
    if request.method == 'GET':
        return render_template('tools_image_link.html')
    if request.method == 'POST':
        # 接收表单传过来的数据
        hospital = request.form.get('hospital')
        input_name = request.form.get('patcard')
        print('患者号:' + input_name)
        cur_timestamp = str(int(round(time.time() * 1000)))
        myTools = MyTools()
        if hospital and input_name:
            # 如果选择省医，则生成省医的链接
            if hospital == 'sy':
                stringSignTemp = 'patId=' + input_name + '&SecretId=5679322D9D58D8311C8207B0933B1B7F&SecretKey=6921CD7EB40229ED47F11641076BE708'
                # 对字符串加密
                Signature = myTools.process_text('md5', stringSignTemp)
                print(Signature)
                # 拼接生成链接地址
                cloudImgLink = 'https://gxykdx.51mdt.cn/gdsy/SYimage/View.html?patId=' + input_name + '&SecretId=5679322D9D58D8311C8207B0933B1B7F&Signature=' + Signature

            # 如果选择中二，则生成中二的链接
            elif hospital == 'ze':
                stringSignTemp = 'patCard=' + input_name + '&orgCode=ZA020603' + '&SecretId=ZSEY5679322D9D58D8311C8207B0933B&SecretKey=ZSEY6921CD7EB40229ED47F11641071B'
                # 对字符串加密
                Signature = myTools.process_text('md5', stringSignTemp)
                # 拼接生成链接地址
                cloudImgLink = 'https://atyjp.gzsys.org.cn/zsey/imagesExam.html?patCard=' + input_name + '&orgCode=ZA020603' + '&SecretId=ZSEY5679322D9D58D8311C8207B0933B&Signature=' + Signature

            # 如果选择慈铭，则生成慈铭的链接
            elif hospital == 'cm':
                # 判断输入框的值是phone还是openid
                if len(input_name) == 11:
                    stringSignTemp = 'phone=' + input_name + '&orgCode=bjcmtjzx&' + 'timeStamp=' + cur_timestamp + '&SecretId=ZSEY5679322D9D58D8311C8207B0933B&SecretKey=ZSEY6921CD7EB40229ED47F11641071B'
                    # 对字符串加密
                    Signature = myTools.process_text('md5', stringSignTemp)
                    # 拼接生成链接地址
                    cloudImgLink = 'https://bjcmtjzx.51mdt.cn/cm/medicalDataCards.html?phone=' + input_name + '&orgCode=bjcmtjzx&' + 'timeStamp=' + cur_timestamp + '&SecretId=ZSEY5679322D9D58D8311C8207B0933B&Signature=' + Signature
                else:
                    stringSignTemp = 'openId=' + input_name + '&orgCode=bjcmtjzx&' + 'timeStamp =' + cur_timestamp + '&SecretId=ZSEY5679322D9D58D8311C8207B0933B&SecretKey=ZSEY6921CD7EB40229ED47F11641071B'
                    # 对字符串加密
                    Signature = myTools.process_text('md5', stringSignTemp)
                    # 拼接生成链接地址
                    cloudImgLink = 'https://bjcmtjzx.51mdt.cn/cm/medicalDataCards.html?openId=' + input_name + '&orgCode=bjcmtjzx&' + 'timeStamp =' + cur_timestamp + '&SecretId=ZSEY5679322D9D58D8311C8207B0933B&Signature=' + Signature

            # 如果选择云肿，则生成云肿的链接
            elif hospital == 'yz':
                stringSignTemp = 'patCard=' + input_name + '&orgCode=YNSZLYY' + '&SecretId=YNZL5679322D9D58D8343&timeStamp=' + cur_timestamp + '&SecretKey=YNZL6921CD7EB40229E13'
                print('当前时间戳:' + cur_timestamp)
                print('Signature加密前:' + stringSignTemp)
                # 对字符串加密
                Signature = myTools.process_text('md5', stringSignTemp)
                print('Signature加密后:' + Signature)
                # 拼接生成链接地址
                cloudImgLink = 'https://yunnan1.51mdt.cn/ynszlyyProxy/cloudImage/imagesExam.html?patCard=' + input_name + '&orgCode=YNSZLYY' + '&SecretId=YNZL5679322D9D58D8343&timeStamp=' + cur_timestamp \
                               + '&Signature=' + Signature
                print('生成链接:' + cloudImgLink)
            # 如果选择中肿，则生成云肿的链接
            elif hospital == 'zz':
                patientname = input_name[0:input_name.rfind(',')]
                patientid = input_name[input_name.rfind(',') + 1:]
                # 对象实例化(患者病历号,患者姓名,0医生 1患者,0APP 1小程序 2PC)
                mrsa = MyHandleRSA(patientid, patientname, '1', '0')
                mrsa_pdf = MyPdfHandleRSA(patientid, patientname, '1', '0')
                link = mrsa.get_link()
                link_pdf = mrsa_pdf.get_link()
                cloudImgLink = f'APP嵌入链接：\n{link}\n\nPDF二维码链接：\n{link_pdf}'
            return render_template('tools_image_link.html', cloudImgLink=cloudImgLink)
        else:
            return render_template('tools_image_link.html')


# 省医云影像链接生成
@app.route('/GetSyImageLink', methods=['GET', 'POST'])
def get_sy_image_link():
    if request.method == 'GET':
        return render_template('GetSyImageLink.html')
    if request.method == 'POST':
        # 接收表单传过来的数据
        input_id = request.form.get('patcard')
        input_pwd = request.form.get('pwd')
        myTools = MyTools()
        md5_pwd = myTools.process_text('md5', input_pwd)
        print(f'患者编号：{input_id},查询密码：{input_pwd}')
        if input_id and md5_pwd == 'F9FE620306785544E9414479E7F2ACBE':
            stringSignTemp = 'patId=' + input_id + '&SecretId=5679322D9D58D8311C8207B0933B1B7F&SecretKey=6921CD7EB40229ED47F11641076BE708'
            # 对字符串加密
            Signature = myTools.process_text('md5', stringSignTemp)
            print(Signature)
            # 拼接生成链接地址
            cloudImgLink = 'https://gxykdx.51mdt.cn/gdsy/SYimage/View.html?patId=' + input_id + '&SecretId=5679322D9D58D8311C8207B0933B1B7F&Signature=' + Signature
            return render_template('GetSyImageLink.html', cloudImgLink=cloudImgLink)
        else:
            return render_template('GetSyImageLink.html')

# CDS接口调试
def get_patient_info_gdsy(patientid):
    """
    通过患者ID获取患者信息（广东省人民医院）。

    :param patientid: 患者ID。
    :return: 包含患者信息的字典。
    """
    try:
        ws = create_connection('wss://gxykdx.51mdt.cn/ws/gdsy/')  # 创建websocket连接
        send_msg = f'56;<root><InterfaceName>PROC_GET_PATIENTINFO_BY_PATIENTID</InterfaceName><Param><PATIENTID>{patientid}</PATIENTID></Param></root>'
        ws.send(send_msg)
        response_msg = ws.recv()  # 获取请求数据
        print(response_msg)
        datas = zlib.decompress(response_msg[8:]).decode('utf-8')  # 解析二进制数据为xml
        print(datas)
        root = ET.fromstring(datas)  # 解析xml数据
        resp = root[0][0].findall('response')[0]
        patient_info = {
            'PATIENTNAME': resp.find('PATIENTNAME').text,
            'PATIENTID': resp.find('PATIENTID').text,
            'IDNO': resp.find('IDNO').text,
            'GENDER': '男' if resp.find('GENDER').text == '1' else ('女' if resp.find('GENDER').text == '2' else '未知'),
            'AGE': resp.find('AGE').text,
            'PHONE': resp.find('PHONE').text
        }
        print(patient_info)
        return patient_info
    except Exception as e:
        return f'接口异常：{e}'


def get_patient_idno_by_cardno(patientid):
    """
    通过患者卡号获取患者身份证号（中山大学附属肿瘤医院）。

    :param patientid: 患者卡号。
    :return: 患者身份证号。
    """
    try:
        ws = create_connection('wss://ydpacs.sysucc.org.cn:9875/')  # 创建websocket连接
        send_msg = f'56;<root><InterfaceName>PROC_GET_PATIENTIDNO_BYCARDNO</InterfaceName><Param><CRADNONO>{patientid}</CRADNONO></Param></root>'
        ws.send(send_msg)
        print(send_msg)
        response_msg = ws.recv()  # 获取请求数据
        datas = zlib.decompress(response_msg[8:]).decode('utf-8')  # 解析二进制数据为xml
        print(datas)
        root = ET.fromstring(datas)  # 解析xml数据
        resp = root[0][0].findall('response')[0]
        patient_idno = resp.find('IDNO').text
        print(patient_idno)
        return patient_idno
    except Exception as e:
        return f'接口异常：{e}'


@app.route('/tools_cds_test', methods=['POST', 'GET'])
def tools_cds_test():
    if request.method == 'GET':
        return render_template('tools_cds_test.html', state='')
    if request.method == 'POST':
        orgname = request.form.get('orgname')
        patientid = request.form.get('patientid')

        if orgname == '广东省人民医院' and patientid:
            # 获取患者信息
            patient_info = get_patient_info_gdsy(patientid)
            return render_template('tools_cds_test.html', datas=[patient_info], state='sy_success')

        elif orgname == '中山大学附属肿瘤医院' and patientid:
            # 获取患者身份证号
            patient_idno = get_patient_idno_by_cardno(patientid)
            return render_template('tools_cds_test.html', data=patient_idno, state='zz_success')

        else:
            return render_template('tools_cds_test.html', state='')

    else:
        return render_template('tools_cds_test.html', state='')


# 字符串处理
def process_text(my_tools, option, original_text):
    """
    处理文本根据选项。

    :param my_tools: MyTools 实例。
    :param option: 处理选项。
    :param original_text: 原始文本。
    :return: 处理后的文本。
    """
    options_mapping = {
        'bs4encode': my_tools.process_text('bs4encode', original_text),
        'bs4decode': my_tools.process_text('bs4decode', original_text),
        'urlencode': my_tools.process_text('urlencode', original_text),
        'urldecode': my_tools.process_text('urldecode', original_text),
        'str2ascii': my_tools.process_text('str2ascii', original_text),
        'ascii2str': my_tools.process_text('ascii2str', original_text),
        'jsonformat': my_tools.process_text('jsonformat', original_text),
        'md5': my_tools.process_text('md5', original_text),
        'translate': my_tools.process_text('translate', original_text),
        'time_timestamp': my_tools.process_text('time_timestamp', original_text),
    }
    return options_mapping.get(option, '')


@app.route('/tools_handle_str', methods=['GET', 'POST'])
def tools_handle_str():
    original_text = ''
    result_text = ''
    my_tools = MyTools()  # Create an instance of MyTools

    if request.method == 'POST':
        original_text = request.form.get('original_text')
        option = request.form.get('option')

        if original_text and option:
            result_text = process_text(my_tools, option, original_text)

    return render_template('tools_handle_str.html', original_text=original_text, result_text=result_text)


# 字典信息查询
def read_dict_file(file_path, fields):
    """
    读取字典文件并组织数据。

    :param file_path: 字典文件路径。
    :param fields: 字典字段名称。
    :return: 组织后的字典数据列表。
    """
    result = []
    with open(file_path, encoding='utf-8') as fin:
        for line in fin:
            data = line.strip().split('\t')
            result.append({fields[i]: data[i] for i in range(len(fields))})
    return result


@app.route('/tools_dict_info')
def get_dict_info():
    errcode_fields = ['num', 'code', 'explain', 'mark']
    message_fields = ['message_num', 'message_id', 'message_name']
    pathtype_fields = ['pathtype_num', 'pathtype_name', 'pathtype_mark']
    ic_status_fields = ['status_num', 'status_id', 'status_mark']

    errcode_result = read_dict_file('./static/txt/cds_errcode.txt', errcode_fields)
    message_result = read_dict_file('./static/txt/cds_message.txt', message_fields)
    pathtype_result = read_dict_file('./static/txt/cds_img_pathtype.txt', pathtype_fields)
    ic_status_result = read_dict_file('./static/txt/ic_img_status.txt', ic_status_fields)

    return render_template('tools_dict_info.html', errcode_result=errcode_result, message_result=message_result,
                           pathtype_result=pathtype_result, ic_status_result=ic_status_result)


# ChatGPT
# 配置 OpenAI API 密钥和基础路径
OPENAI_API_KEY = "sk-2bpI07Cu1PWmmKweFKvpHnNKDxCSsnJRYPtLNO90rXPH7VLt"
OPENAI_API_BASE = "https://api.chatanywhere.com.cn/v1"
openai.api_key = OPENAI_API_KEY
openai.api_base = OPENAI_API_BASE


@app.route('/tools_chatgpt')
def tools_chatgpt():
    return render_template('tools_chatgpt.html')


@app.route('/chatgpt-clone', methods=['POST', 'GET'])
def chatgpt_clone():
    question = request.args.get('question', '').strip()
    if question:
        try:
            def stream():
                result = openai.ChatCompletion.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "user", "content": question}
                    ],
                    stream=True
                )
                print(result)
                for chunk in result:
                    if chunk['choices'][0]["finish_reason"] is not None:
                        data = "[DONE]"
                    else:
                        data = chunk["choices"][0]["delta"].get("content", "")
                        print(data)
                    yield "data: %s\n\n" % data.replace("\n", "<br />")

            return Response(stream(), mimetype="text/event-stream")
        except Exception as e:
            return str(e)
    return '请输入内容'


if __name__ == "__main__":
    # 运维助手
    app.run(host='0.0.0.0', port=9900, debug=False)
