import time
# pip install pillow
from PIL import Image, ImageDraw, ImageFont
import json
import requests
from selenium import webdriver
from selenium.webdriver import ChromeOptions  # 规避检测
from selenium.webdriver.common.by import By
import base64


class MyVersionManage():
    def __init__(self):
        pass

    # 获取版本组合列表
    def get_version_group_list(self):
        url = 'https://ci.annetinfo.com/version_hosting/auth/login'
        headers = {
            "Cookie": "session=2329a171-f1f5-46cc-a9de-0e9dd2b8d590.yy9gXDhLWbi91uywz_sypSooE6Y",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }

        data = {
            "username": "admin",
            "password": "annet_version_admin"
        }
        # 登录
        r = requests.post(url, headers=headers, data=data)
        if r.status_code != 200:
            raise Exception('error')
        resp = requests.get('https://ci.annetinfo.com/version_hosting/group/list', headers=headers)
        resp_dict = json.loads(resp.content)
        version_list = []
        for data in resp_dict['data']:
            dowmload_url = 'https://ci.annetinfo.com/annetweb/pages/download/download?group_uuid=' + data.get('uuid')
            version_list.append({
                'name': data['name'],
                # 'uuid': data['uuid'],
                # 'url': data['url'],
                'description': data.get('description'),
                'all_counts': data.get('all_counts'),
                'ios_counts': data.get('ios_counts'),
                'android_counts': data.get('android_counts'),
                'dowmload_url': dowmload_url
            })
        return version_list

    # version_list = get_dowmload_urls()
    # print(version_list)

    # 下载应用logo和二维码
    def download_base_img(self, url):
        # 实现无界面浏览器
        option = ChromeOptions()
        option.add_argument("--headless")
        option.add_argument('window-size=1920x1080')
        browser = webdriver.Chrome(options=option)
        browser.get(url)
        browser.implicitly_wait(30)
        browser.save_screenshot('static/app/screen.png')
        #
        app_title = browser.find_element(By.XPATH,
                                         '/html/body/uni-app/uni-page/uni-page-wrapper/uni-page-body/uni-view/uni-view[2]/uni-view/uni-view/uni-view[2]/uni-text/span').text

        app_logo_url = browser.find_element(By.XPATH,
                                            '/html/body/uni-app/uni-page/uni-page-wrapper/uni-page-body/uni-view/uni-view[2]/uni-view/uni-view/uni-view[1]/uni-image/img').get_attribute(
            'src')
        app_qrcode_base64 = browser.find_element(By.XPATH, '//*[@id="qrcode"]/img').get_attribute('src')
        imgdata = base64.b64decode(app_qrcode_base64.split(',')[1])
        # print(app_title,app_logo_url,app_qrcode_base64)

        r = requests.get(app_logo_url)
        with open(f'static/app/base_imgs/{app_title}-logo.png', 'wb') as f1, open(
                f'static/app/base_imgs/{app_title}-code.png', 'wb') as f2:
            f1.write(r.content)
            f2.write(imgdata)

    # 拼接生成应用下载二维码
    def img_synthesis(self, name):
        # 加载底图
        base_img = Image.open('static/app/base2.png')
        # 查看图片size和mode
        # print(base_img.size)
        # print(base_img.mode)
        # 底图需要P掉的位置
        box = (250, 120, 450, 320)
        box2 = (372, 400, 872, 900)

        # 加载需要P上去的logo图片
        logo_img = Image.open(f'static/app/base_imgs/{name}-logo.png')
        # 这里可以选择一块区域或者整张图片
        # region = tmp_img.crop((0,0,304,546)) #选择一块区域
        # 或者使用整张图片
        region = logo_img

        # 加载需要P上去的二维码图片
        qrcode_img = Image.open(f'static/app/base_imgs/{name}-code.png')
        # 这里可以选择一块区域或者整张图片
        # region = tmp_img.crop((0,0,304,546)) #选择一块区域
        # 或者使用整张图片
        region2 = qrcode_img

        # 使用 paste(region, box) 方法将图片粘贴到另一种图片上去.
        # 注意，region的大小必须和box的大小完全匹配。但是两张图片的mode可以不同，合并的时候回自动转化。如果需要保留透明度，则使用RGMA mode
        # 提前将图片进行缩放，以适应box区域大小
        # region = region.rotate(180) #对图片进行旋转
        region = region.resize((box[2] - box[0], box[3] - box[1]))
        region2 = region2.resize((box2[2] - box2[0], box2[3] - box2[1]))
        base_img.paste(region, box)
        base_img.paste(region2, box2)

        # 添加文字，根据名称长度做相应的处理
        if len(name) <= 4:
            ttfont = ImageFont.truetype("C:/Windows/Fonts/STHUPO.TTF", 120)
            draw = ImageDraw.Draw(base_img)
            draw.text((480, 160), f'{name}', fill=(255, 255, 255), font=ttfont)
        elif 4 < len(name) <= 8:
            ttfont = ImageFont.truetype("C:/Windows/Fonts/STHUPO.TTF", 90)
            draw = ImageDraw.Draw(base_img)
            draw.text((480, 175), f'{name}', fill=(255, 255, 255), font=ttfont)
        elif len(name) == 11:
            ttfont = ImageFont.truetype("C:/Windows/Fonts/STHUPO.TTF", 60)
            draw = ImageDraw.Draw(base_img)
            draw.text((480, 135), f'{name}'[0:7] + '\n' * 2 + f'{name}'[7:], fill=(255, 255, 255), font=ttfont)
        else:
            ttfont = ImageFont.truetype("C:/Windows/Fonts/STHUPO.TTF", 60)
            draw = ImageDraw.Draw(base_img)
            draw.text((480, 135), f'{name}'[0:8] + '\n' * 2 + f'{name}'[8:], fill=(255, 255, 255), font=ttfont)
        # base_img.show()  # 查看合成的图片
        base_img.save(f'static/app/version_imgs/{name}.png')  # 保存图片


if __name__ == '__main__':
    myVersionManage = MyVersionManage()
    version_list = myVersionManage.get_version_group_list()
    for data in version_list:
        print(data['dowmload_url'])
        # 下载logo和二维码
        # myVersionManage.download_base_img(data['dowmload_url'])

        # 拼接生成二维码
        # myVersionManage.img_synthesis(data['name'])
        time.sleep(3)
