// 复制文本到剪贴板
// function copy_text(id) {
//     let value = document.getElementById(id).value;
//     if (value != '') {
//         window.navigator.clipboard.writeText(value).then(() => {
//             alert('链接复制成功！');
//         });
//     }
// }

function copy_text(id) {
    const textArea = document.getElementById(id);
    const value = textArea.value
    if (value != '') {
        textArea.select();
        document.execCommand('copy');
        alert('链接复制成功！');
    }
}