{% include 'header.html' %}
<style>
    div.copy {
        text-align: right !important;
    }
</style>
<div class="container">
    {% if session['username'] == null %}
        <h1>欢迎使用运维助手，请先登录！</h1>
    {% else %}
        <div class="container">
            <h3>ChatGPT</h3>
            <form action="/chatgpt-clone" method="post">
                <p for="question">简单描述一下你的问题：</p>
                <div class="input-group">
                    <input type="text" class="form-control" id="question" name="question"
                           placeholder="提问技巧：赋予角色-要解决的问题-合适的返回格式">
                    <input class="btn btn-primary" type="button" id="submitbtn" value="开始提问"></input>
                </div>
                <p>AI回答：</p>
                <div class="input-group">
                    <textarea class="form-control" id="result" name="result" rows="16" placeholder="三条使用哲学:
1、它是“生成型”，不是“真理型”
2、它最擅长的是“助理”类工作
3、它没有情感，但可以共鸣;它没有意志，但有价值观"></textarea>
                </div>
                <div class="copy">
                    <input class="btn btn-primary" type="button" name="copy" value="复制结果" onclick="copy_text('result')">
                    </input>
                </div>
            </form>
        </div>
    {% endif %}
</div>
<script type="text/javascript">
    $(function () {
        $("#submitbtn").click(function () {
            $("#result").html("请求成功，等待数据加载...")
            var source = new EventSource("/chatgpt-clone?question=" + $("#question").val())
            var begin_output = false
            source.onmessage = function (event) {
                if (begin_output === false) {
                    begin_output = true
                    $("#result").html("")
                }
                if (event.data == "[DONE]") {
                    source.close()
                } else
                    $("#result").html($("#result").html() + event.data)
            }
        })
    })
</script>
{% include 'footer.html' %}