import pymysql


class MyConnectMySQL():
    def __init__(self):
        pass

    def get_conn(self):
        return pymysql.Connect(
            host='annet-mysql1.mysql.rds.aliyuncs.com',
            port=3306,
            user='master_annet',
            password='!@#Annet123',
            database='annet_master20171116',
            charset='utf8'
        )

    # def get_conn():
    #     return pymysql.Connect(
    #         host='17vgilwu.2404.dnstoo.com',
    #         port=5502,
    #         user='jimlee_f',
    #         password='jimlee',
    #         database='jimlee',
    #         charset='utf8'
    #     )

    # 查询表
    def quary_data(self, sql):
        conn = self.get_conn()
        try:
            consor = conn.cursor(pymysql.cursors.DictCursor)
            consor.execute(sql)
            return consor.fetchall()
        finally:
            conn.close()

    # 修改更新表
    def insert_or_update_table(self, sql):
        conn = self.get_conn()
        try:
            consor = conn.cursor()
            consor.execute(sql)
            conn.commit()
            return consor.fetchall()
        finally:
            conn.close()


if __name__ == '__main__':

    myConnectMySQL = MyConnectMySQL()
    phone_info = '18292668614'
    if len(phone_info) != 0:
        sql = f'''
                SELECT DISTINCT
                    a.name,
                    a.role,
                    a.userid,
                    a.phone,
                    b.password,
                    FROM_UNIXTIME(LEFT(b.modify_password_time,10)) modify_password_time,
                    b.fail_num  
                FROM user_baseinfo a
                LEFT JOIN user b on a.userid=b.userid
                WHERE a.phone='{phone_info}'
                '''
        user_baseinfo = myConnectMySQL.quary_data(sql)
        print(user_baseinfo)
