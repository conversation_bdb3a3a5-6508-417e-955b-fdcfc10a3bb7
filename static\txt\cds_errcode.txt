0	error000	正常	-
1	error001	Socket超时	一般是网络问题
2	error002	报文格式错误或者CDS不支持此报文	一般是报文格式与要求的格式对不上，或者CDS版本过低还不支持当前请求报文
5	error005	报文参数错误	CDS无法解析报文部分入参，或者根据入参无法拿到响应的数据（如根据入参调用流程失败）
6	error006	接收前端数据失败	数据需要分块传输的时候，一般是网络问题
7	error007	发送数据给前端失败	数据需要分块传输的时候，一般是网络问题
8	error008	内存错误	CDS无法获取数据或者内部接口（如压缩，分配内存等）调用失败
9	error009	本地文件不存在	需要解析的本地文件不存在或者文件下载到本地时失败
10	error010	连接数据库失败	需要连接数据库的报文中连接数据库失败
43	error043	图像信息解析失败	一般出现在5号报文无法获取影像数据
48	error048	操作数据库失败	需要执行SQL的报文中执行SQL失败
49	error049	内存中找不到数据	与error008部分重叠，但是特定只无法在CDS内存中获取到数据
50	error050	编码失败	一般发生在CDS调用内存接口（如格式化，编码格式转换）出现
51	error051	压缩失败	压缩数据失败
53	error053	病人已经出院	获取病人信息时，无法在在院病人信息中查找到该病人
54	error054	等待归档完成	前端请求小图信息时，CDS正在归档检查，通知前端等待归档完成
55	error055	token验证失败	报文参数中的token与CDS内存中对不上，一般是密码错误
56	error056	当前数据账号已经被绑定	一般在医院内部，不允许两个userid绑定同一个data_account
57	error057	当前数据账号已经被解绑	CDS发现前端请求报文中的userid和data_account已经没有关联关系了，通知前端
58	error058	资源权限校验失败	当前账号没有资源权限（如电子病历，医嘱，院长驾驶舱等）
59	error059	当前检查归档失败	无法获取小图信息
60	error060	流程不存在	CDS的interfaces目录下无法找到前端请求的流程
61	error061	影像中心正在下载影像	存在影像中心的医院，如果当前检查正在归档，前端请求时CDS会返回此通知
62	error062	影像不存在	确定当前检查没有影像数据
63	error063	报告未审核	中二需求：未审核的报告前端不能查看
64	error064	未提交解析数据	-
65	error065	数据解析未完成	-
66	error066	数据账号和手机号不匹配	龙岗需求：数据账号需要与手机号唯一匹配