# 中肿云取片链接生成规则
# Server使用Client的公钥对内容进行rsa 加密
import hashlib
import json
import time
import base64
from Crypto.PublicKey import RSA  # 先安装pycryptodome，再安装Crypto，将site-packages下文件夹crypto改为Crypto
from Crypto.Cipher import PKCS1_v1_5 as Cipher_PKC


# pip install pycryptodome
# base64加密,对编码字符进行urlencode处理y

def bs64_encrypt_text(text):
    bytes_str = text.encode("utf-8")
    str_url = base64.b64encode(bytes_str).decode("utf-8")  # 被编码的参数必须是二进制数据
    return str_url


class MyHandleRSA:
    def __init__(self, patientid, patientname, identity, origin):
        self.patientid = patientid
        self.patientname = patientname
        self.identity = identity
        self.origin = origin
        self.timeStamp = int(round(time.time() * 1000))
        self.clinet_publprint = """-----BEGIN PUBLprint KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDIySVTsyirvE4ojMHfpCUdZ9V/xwxtlUmechP8NO8jYUb/JcGWwb0+0cFqADMSedXh32oMgk3V8i+j/AuDDm1ABfjSjKHKYDBsbMEiKnFYbTDLMnPB3zUzwjPVHNdzlbJ5k1WnWGgxN/FH8JYs04NsB9cWtj3JER6fhx4dYs6CuQIDAQAB
-----END PUBLprint KEY-----"""
        # print(self.timeStamp)

    # 获取要签名的数据
    def get_plaintext(self):
        params_pat = {
            "treatmentCard": self.patientid,  # 患者病历号
            "treatOrgCode": "45541700-5",  # 机构编码
            "treatName": self.patientname,  # 患者姓名
            "timestamp": self.timeStamp  # 当前时间戳
        }
        # 字典转成json字符串
        plaintext = json.dumps(params_pat, ensure_ascii=False).replace(" ", "")
        print(plaintext)
        return plaintext

    # 生成签名
    def get_sign(self):
        plaintext = self.get_plaintext()
        """
        client 公钥进行加密
        plaintext:需要加密的明文文本，公钥加密，私钥解密
        """
        # 加载公钥
        rsa_key = RSA.import_key(self.clinet_publprint)
        # 加密
        cipher_rsa = Cipher_PKC.new(rsa_key)
        en_data = cipher_rsa.encrypt(plaintext.encode("utf-8"))  # 加密
        # base64 进行编码，返回字符串
        sign = base64.b64encode(en_data).decode()
        return sign  # 返回字符串

    # 生成APP或小程序访问链接
    def get_link(self):
        sign = self.get_sign()
        # 患者APP嵌入
        params_pat = {
            "treatmentCard": self.patientid,  # 患者病历号
            "treatOrgCode": "45541700-5",  # 机构编码
            "treatName": self.patientname,  # 患者姓名
            "identity": self.identity,  # 0医生 1患者
            "origin": self.origin,  # 0APP 1小程序 2PC
            "sign": sign  # 加密签名
        }
        # 字典转成json字符串
        params_pat = json.dumps(params_pat, ensure_ascii=False).replace(" ", "")
        print(params_pat)
        params_pat_bs64 = bs64_encrypt_text(params_pat)
        link = 'https://ydpacs.sysucc.org.cn/CloudImageNew/#/?params=' + params_pat_bs64
        print(params_pat_bs64)
        return link


class MyPdfHandleRSA:
    def __init__(self, patientid, patientname, identity, origin):
        self.patientid = patientid
        self.patientname = patientname
        self.identity = identity
        self.origin = origin
        self.timeStamp = int(round(time.time() * 1000))
        self.clinet_publprint = """-----BEGIN PUBLprint KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDIySVTsyirvE4ojMHfpCUdZ9V/xwxtlUmechP8NO8jYUb/JcGWwb0+0cFqADMSedXh32oMgk3V8i+j/AuDDm1ABfjSjKHKYDBsbMEiKnFYbTDLMnPB3zUzwjPVHNdzlbJ5k1WnWGgxN/FH8JYs04NsB9cWtj3JER6fhx4dYs6CuQIDAQAB
-----END PUBLprint KEY-----"""
        # print(self.timeStamp)

    # 获取要签名的数据
    def get_plaintext(self):
        params_pat = {
            "treatmentCard": self.patientid,  # 患者病历号
            "treatOrgCode": "45541700-5",  # 机构编码
            "treatName": self.patientname,  # 患者姓名
            "timestamp": self.timeStamp  # 当前时间戳
        }
        # 字典转成json字符串
        plaintext = json.dumps(params_pat, ensure_ascii=False).replace(" ", "")
        return plaintext

    # 生成签名
    def get_sign(self):
        plaintext = self.get_plaintext()
        print(plaintext)
        """
        client 公钥进行加密
        plaintext:需要加密的明文文本，公钥加密，私钥解密
        """
        # 加载公钥
        rsa_key = RSA.import_key(self.clinet_publprint)
        # 加密
        cipher_rsa = Cipher_PKC.new(rsa_key)
        en_data = cipher_rsa.encrypt(plaintext.encode("utf-8"))  # 加密
        # print(en_data)
        # base64 进行编码，返回字符串
        sign = base64.b64encode(en_data).decode()
        return sign  # 返回字符串

    # 生成APP或小程序访问链接
    def get_link(self):
        sign = self.get_sign()
        print(sign)
        # 患者APP嵌入
        params_pat = {
            "treatmentCard": self.patientid,  # 患者病历号
            "treatOrgCode": "45541700-5",  # 机构编码
            "treatName": self.patientname,  # 患者姓名
            "identity": self.identity,  # 0医生 1患者
            "origin": self.origin,  # 0APP 1小程序 2PC
            "sign": sign  # 加密签名
        }
        # 字典转成json字符串
        params_pat = json.dumps(params_pat, ensure_ascii=False).replace(" ", "")
        print(params_pat)
        params_pat_bs64 = bs64_encrypt_text(params_pat)
        link = 'https://ydpacs.sysucc.org.cn/CloudImageNew/#/?params=' + params_pat_bs64

        print(params_pat_bs64)
        print(link)
        return link


if __name__ == '__main__':
    # 对象实例化(患者病历号,患者姓名,0医生 1患者,0APP 1小程序 2PC)
    # 查询患者端-APP
    mrsa = MyPdfHandleRSA('**********', '张三', '1', '0')
    # 获取查看链接
    mrsa.get_link()
