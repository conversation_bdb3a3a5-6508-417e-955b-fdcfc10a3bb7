{% include 'header.html' %}
<div class="container">
    {% if session['username'] == null %}
        <h3>欢迎使用运维助手，请先登录！</h3>
    {% else %}
        <div>
            <h3>手机验证码</h3>
            <table class="table table-striped table-hover table-bordered">
                <tr>
                    <th>验证码</th>
                    <th>手机号</th>
                    <th>创建时间</th>
                    <th>过期时间</th>
                </tr>
                {% for reslut in user_phone_code %}
                    <tr>
                        <td>{{ reslut['security_code'] }}</td>
                        <td>{{ reslut['phone'] }}</td>
                        <td>{{ reslut['create_time'] }}</td>
                        <td>{{ reslut['expire_time'] }}</td>
                    </tr>
                {% endfor %}
            </table>
        </div>
        <div>
            <h3>用户基本信息</h3>
            <table class="table table-striped table-hover table-bordered">
                <tr>
                    <th>姓名</th>
                    <th>角色</th>
                    <th>用户ID</th>
                    <th>手机号</th>
                    <th>密码</th>
                    <th>密码修改时间</th>
                    <th>登录错误次数</th>
                </tr>
                {% for reslut in user_baseinfo %}
                    <tr>
                        <td>{{ reslut['name'] }}</td>
                        <td>{{ reslut['role'] }}</td>
                        <td>{{ reslut['userid'] }}</td>
                        <td>{{ reslut['phone'] }}</td>
                        <td>{{ reslut['password'] }}</td>
                        <td>{{ reslut['modify_password_time'] }}</td>
                        <td>{{ reslut['fail_num'] }}</td>
                    </tr>
                {% endfor %}
            </table>
        </div>
        <div>
            <h3>用户绑定信息</h3>
            <table class="table table-striped table-hover table-bordered">
                <tr>
                    <th>数据账号</th>
                    <th>姓名</th>
                    <th>机构名称</th>
                    <th>科室名称</th>
                    <th>角色</th>
                    <th>用户ID</th>
                    <th>手机号</th>
                </tr>
                {% for reslut in user_dataaccount %}
                    <tr>
                        <td>{{ reslut['dataaccount'] }}</td>
                        <td>{{ reslut['name'] }}</td>
                        <td>{{ reslut['orgname'] }}</td>
                        <td>{{ reslut['deptname'] }}</td>
                        <td>{{ reslut['office'] }}</td>
                        <td>{{ reslut['userid'] }}</td>
                        <td>{{ reslut['phone'] }}</td>
                    </tr>
                {% endfor %}
            </table>
        </div>
        <div>
            <h3>账号操作记录</h3>
            <table class="table table-striped table-hover table-bordered">
                <tr>
                    <th>创建时间</th>
                    <th>数据账号</th>
                    <th>用户ID</th>
                    <th>操作</th>
                    <th>机构编码</th>
                    <th>Token</th>
                </tr>
                {% for reslut in operation_record %}
                    <tr>
                        <td>{{ reslut['create_date'] }}</td>
                        <td>{{ reslut['data_account'] }}</td>
                        <td>{{ reslut['user_id'] }}</td>
                        <td>{{ reslut['operation'] }}</td>
                        <td>{{ reslut['org_code'] }}</td>
                        <td>{{ reslut['data_token'] }}</td>
                    </tr>
                {% endfor %}
            </table>
        </div>
        <div>
            <h3>医院信息</h3>
            <table class="table table-striped table-hover table-bordered">
                <tr>
                    <th>机构名称</th>
                    <th>机构编码</th>
                    <th>CdsIP</th>
                    <th>Socket端口</th>
                    <th>WebSocket端口</th>
                    <th>Cds版本</th>
                    <th>是否支持水印</th>
                    <th>水印配置</th>
                    <th>医院Logo</th>
                    <th>创建时间</th>
                </tr>
                {% for reslut in organization_config %}
                    <tr>
                        <td>{{ reslut['org_name'] }}</td>
                        <td>{{ reslut['org_code'] }}</td>
                        <td>{{ reslut['cds_ip'] }}</td>
                        <td>{{ reslut['cds_port'] }}</td>
                        <td>{{ reslut['cds_web_socket_port'] }}</td>
                        <td>{{ reslut['cds_version'] }}</td>
                        <td>{{ reslut['is_watermark'] }}</td>
                        <td>{{ reslut['water_mark_config'] }}</td>
                        <td>{{ reslut['logo_url'] }}</td>
                        <td>{{ reslut['create_time'] }}</td>
                    </tr>
                {% endfor %}
            </table>
        </div>
    {% endif %}
</div>
{% include 'footer.html' %}
<script>
    var tables = document.getElementsByTagName('table');
    for (var i = 0; i < tables.length; i++) {
        var num = tables[i].children[0].children.length;
        console.log(num)
        // 判断如果tr标签数为1，即无查询结果，则隐藏该部分
        if (num == 1) {
            //tables[i].removeChild(tables[i]);
            tables[i].parentElement.style.display = 'none';
        }
    }
</script>
</body>
</html>