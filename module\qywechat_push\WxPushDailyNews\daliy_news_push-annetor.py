import datetime
import json
import time
import requests
from bs4 import BeautifulSoup
from PIL import Image, ImageDraw, ImageFont
import traceback

'''
本文件主要实现通过企业微信应用给企业成员发消息
1、IP加到白名单：应用-企业可信IP中
2、微信接收通知：我的企业-微信插件-邀请关注
3、每天9:00获取每日新闻数据
4、推送到微信
'''


# 每日新闻推送
class DaliyNewsPush:
    def __init__(self):
        # 企业微信api
        self.CORP_ID = "ww88d75674bd4d4447"  # 我的企业中查看企业ID
        # 应用中查看Secret，每日简报-1000003
        self.SECRET = 'Mc36_bzR_sjmxWMAFbkEsVEqG4y52fhZwkYhN9y8OVE'
        self.session = requests.session()

    # 获取今日新闻数据
    def get_today_news(self):
        try:
            url = "https://www.zhihu.com/api/v4/columns/c_1261258401923026944/items"
            headers = {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36"
            }
            r = requests.get(url, headers=headers)
            data_dict = json.loads(r.text)

            for data in data_dict['data']:
                # 时间戳传唤为日期
                timeStamp = data['created']
                timeArray = time.localtime(timeStamp)
                otherStyleTime = time.strftime("%Y-%m-%d", timeArray)
                today = datetime.date.today().strftime('%Y-%m-%d')
                # 判断只取今日的消息
                if today == otherStyleTime:
                    # 解析content中的html内容
                    soup = BeautifulSoup(data['content'], 'lxml')
                    datas = soup.select('body > p')
                    # print(datas)
                    # 获取标签中的数据，分两部分添加到列表
                    text = []
                    text1 = []
                    text2 = []
                    for item in datas:
                        text.append(item.get_text())
                    for item in datas[1:11]:
                        text1.append(item.get_text() + '\n')
                    for item in datas[11:-1]:
                        text2.append(item.get_text() + '\n')
                    text1_str = ''.join(text1)
                    text2_str = ''.join(text2)
                    # 将今日新闻数据存到本地
                    # with open('../static/txt/push_daily_news.txt', 'r+', encoding='utf-8') as fout:
                    #     content = fout.read()
                    #     fout.seek(0, 0)
                    #     for item in datas:
                    #         fout.write(item.get_text() + '\n')
                    #     fout.write(content)
                    print('新闻数据获取成功')
                    return text, text1_str, text2_str
        except Exception as e:
            print('新闻数据获取失败：', traceback.format_exc(), e)
            return None

    # 开始绘制成图片
    def draw_pic(self):
        try:
            # 设置字体样式
            # linux服务器字体路径设置
            # font_type = '/usr/share/fonts/chinese/PINGFANG REGULAR.TTF'
            # font_medium_type = '/usr/share/fonts/chinese/PINGFANG REGULAR.TTF'
            # # Windows服务器字体路径设置（报错解决不了啊啊啊...）
            font_type = 'C:/Windows/Fonts/SIMYOU.TTF'
            font_medium_type = 'C:/Windows/Fonts/SIMYOU.TTF'
            header_font = ImageFont.truetype(font_medium_type, 55)
            title_font = ImageFont.truetype(font_medium_type, 25)
            font = ImageFont.truetype(font_type, 40)
            color = "#726053"
            color1 = "#294E76"
            # 加载背景图
            image = Image.open('base.png')
            draw = ImageDraw.Draw(image)

            # 开始画标题
            header = '60s读懂世界'
            header_x = 120
            header_y = 230
            draw.text((header_x, header_y), u'%s' % header, color, header_font)
            # 开始画作者
            title = '小趴菜'
            title_x = header_x
            title_y = header_y + 80
            draw.text((title_x, title_y), u'%s' % title, color1, title_font)
            # 开始画发布时间
            cur_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            cur_time_x = 420
            cur_time_y = title_y
            cur_time_font = ImageFont.truetype(font_type, 25)
            draw.text((cur_time_x, cur_time_y), cur_time, color, cur_time_font)

            # 开始画天气
            try:
                r = requests.get('http://t.weather.sojson.com/api/weather/city/101280101')
                data = json.loads(r.text)
                week = data['data']['forecast'][0]['week']
                type = data['data']['forecast'][0]['type']
                high = data['data']['forecast'][0]['high'].replace('高温 ', '')
                low = data['data']['forecast'][0]['low'].replace('低温 ', '')
                weather = f'{week} {type} {low}~{high}'
            except Exception as e:
                print('天气绘制失败：', traceback.format_exc(), e)
                return None

            weather_x = 780
            weather_y = title_y
            weather_font = ImageFont.truetype(font_type, 25)
            draw.text((weather_x, weather_y), weather, color, weather_font)

            # 绘制新闻内容
            news_x = title_x - 30
            news_y = title_y + 60
            title_text = '【内容摘要】'
            text = self.get_today_news()
            newlist = self.old_to_new_list(font, text[0][3:])
            # 绘制title
            draw.text((news_x, news_y), title_text, color, font)
            for num, info in enumerate(newlist):
                height = num * 75
                # 绘制内容
                draw.text((news_x, news_y + height + 80), info, color, font)
            # image.show()
            cur_day = time.strftime('%Y%m%d', time.localtime(time.time()))
            file_path = f'DaliyNews_{cur_day}.png'
            image.save(file_path)
            print('图片绘制成功')
            return file_path
        except Exception as e:
            print('图片绘制失败：', traceback.format_exc(), e)
            return None

    # 按每行内容长度拆分成新列表
    def old_to_new_list(self, font, oldlist):
        newlist = []
        for single_text in oldlist:
            if font.getsize(single_text.strip())[0] <= 945:
                newlist.append(single_text)
            else:
                newStr = ''
                # 从字符串single_text逐个取字，直到总长度大于750px
                for item in single_text:
                    newStr += item
                    # gitsize可以同时输出字符串的宽和高
                    if font.getsize(newStr.strip())[0] > 945:
                        newlist.append(newStr[:-1])
                        newStr = newStr[-1]
                    else:
                        continue
                newlist.append(newStr)
        return newlist

    # 企业微信-获取token
    def get_token(self):
        url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={self.CORP_ID}&corpsecret={self.SECRET}"
        rep = self.session.get(url)
        if rep.status_code != 200:
            print('token获取失败')
            return None
        token = json.loads(rep.content)['access_token']
        print('token获取成功')
        return token

    # 企业微信-上传临时素材，获取图片ID
    def get_media_id(self):
        token = self.get_token()  # 获取token
        f_name = self.draw_pic()  # 图片路径
        img_url = f'https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token={token}&type=image'
        file = {'p_w_picpath': open(f_name, 'rb')}
        try:
            r = requests.post(img_url, files=file)
            resp = json.loads(r.text)
            print('图片素材上传成功')
            return resp.get('media_id')
        except Exception as e:
            print('图片素材上传失败', traceback.format_exc(), e)
            return None

    # 企业微信-消息推送
    # def send_msg(self, content):
    def send_msg(self):
        media_id = self.get_media_id()
        if media_id:
            try:
                # 图片消息数据
                form_data = {
                    "touser": "LiZhaoMing",
                    "toparty": "1",
                    "totag": ["tagid1 | tagid1"],
                    "toall": 0,
                    "msgtype": "image",
                    "agentid": 1000003,
                    "image": {
                        "media_id": media_id
                    },
                    "safe": 0,
                    "enable_duplicate_check": 0,
                    "duplicate_check_interval": 1800
                }
                # 文本消息数据
                # form_data = {
                #     "touser": "LiZhaoMing",
                #     "toparty": "1",
                #     "totag": ["tagid1 | tagid1"],
                #     "toall": 0,
                #     "msgtype": "text",
                #     "agentid": 1000003,
                #     "text": {
                #         "content": content
                #     },
                #     "safe": 0
                # }
                token = self.get_token()
                url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + token
                header = {
                    "Content-Type": "application/json"
                }
                # 打印消息推送内容
                # print(form_data)
                r = self.session.post(url, data=json.dumps(form_data, ensure_ascii=False).encode('utf-8'),
                                      headers=header)
                if r.status_code != 200:
                    print("发送请求失败！")
                # 打印消息推送后返回的信息
                rep = json.loads(r.content)
                print(f'消息发送成功...')
            except:
                traceback.format_exc()

    # 企业微信-设置定时推送
    def push_msg(self):
        set_time = '08:50'  # 自定义提醒时间
        while True:
            current_time = time.strftime('%H:%M', time.localtime(time.time()))  # 获取为HH:MM时间格式
            cur_time = time.strftime('%Y-%m-%d %H:%M', time.localtime(time.time()))  # 获取为HH:MM时间格式
            if set_time == current_time:
                print(f'到达设定时间:{cur_time}\n准备消息发送...')
                # pygame.mixer.init()
                # 每日简报-数据获取
                # text = self.get_today_news()
                # 每日简报-数据推送
                # 推送新闻图片
                try:
                    self.send_msg()
                    time.sleep(60)
                except:
                    traceback.format_exc()
                    time.sleep(60)

                # 推送新闻内容
                # send_msg(text[1])
                # time.sleep(1)
                # send_msg(text[2])
                # break


if __name__ == "__main__":
    # 企业微信消息推送
    daliyNewsPush = DaliyNewsPush()
    daliyNewsPush.push_msg()
    # daliyNewsPush.get_token()
    # daliyNewsPush.get_media_id()
