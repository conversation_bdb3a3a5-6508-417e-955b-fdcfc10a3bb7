import datetime
import json
import time
import requests
from bs4 import BeautifulSoup

'''
本文件主要实现通过企业微信应用给企业成员发消息
1、IP加到白名单：应用-企业可信IP中
2、微信接收通知：我的企业-微信插件-邀请关注
3、每天9:00获取每日新闻数据
4、推送到微信
'''


# 推送消息数据
class ReadyPushMessage:
    def __init__(self):
        pass

    # 获取今日新闻数据
    def get_today_news(self):
        try:
            url = "https://www.zhihu.com/api/v4/columns/c_1261258401923026944/items"
            headers = {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36"
            }
            r = requests.get(url, headers=headers)
            data_dict = json.loads(r.text)

            for data in data_dict['data']:
                # 时间戳传唤为日期
                timeStamp = data['created']
                timeArray = time.localtime(timeStamp)
                otherStyleTime = time.strftime("%Y-%m-%d", timeArray)
                today = datetime.date.today().strftime('%Y-%m-%d')
                # 判断只取今日的消息
                if today == otherStyleTime:
                    # 解析content中的html内容
                    soup = BeautifulSoup(data['content'], 'lxml')
                    datas = soup.select('body > p')
                    # print(datas)
                    # 获取标签中的数据，分两部分添加到列表
                    text = []
                    text1 = []
                    text2 = []
                    for item in datas:
                        text.append(item.get_text())
                    for item in datas[1:11]:
                        text1.append(item.get_text() + '\n')
                    for item in datas[11:-1]:
                        text2.append(item.get_text() + '\n')
                    text1_str = ''.join(text1)
                    text2_str = ''.join(text2)
                    # 将今日新闻数据存到本地
                    # with open('../static/txt/push_daily_news.txt', 'r+', encoding='utf-8') as fout:
                    #     content = fout.read()
                    #     fout.seek(0, 0)
                    #     for item in datas:
                    #         fout.write(item.get_text() + '\n')
                    #     fout.write(content)


                    return text, text1_str, text2_str
        except Exception as e:
            return e

    # 获取今日新债数据
    def get_today_bonds(self):
        try:
            request_params = {
                "headers": {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.97 Safari/537.36"
                },
                "timeout": 5
            }
            r = requests.get("http://data.hexin.cn/ipo/bond/cate/info/", **request_params)
            # print(json.dumps(r.json(),ensure_ascii=False))
            bonds = json.loads(r.text)
            text = []
            for bond in bonds:
                # today = time.strftime('%Y-%m-%d', time.localtime(time.time()))
                today = datetime.date.today().strftime('%Y-%m-%d')
                if today == bond.get('sgDate'):
                    text.append(f"""今日打新: {bond.get('zqName')} 发行量：{bond.get('issue')}亿""")
            # print('\n'.join(text))
            if text:
                return '\n'.join(text)
            else:
                return None
        except Exception as e:
            return e


# 企业微信接口
class WeChatPub:
    def __init__(self):
        self.CORP_ID = "ww88d75674bd4d4447"  # 我的企业中查看企业ID
        # 应用中查看Secret，新债提醒-1000002，每日简报-1000003，运维监控-1000004
        self.SECRET = ['odKlc2MjaH4Gkf3PtqJoXlOH6KiUiw2wBPaWl0lQA_c', 'Mc36_bzR_sjmxWMAFbkEsVEqG4y52fhZwkYhN9y8OVE',
                       'jrRyvLt2lKE5zjoOu2eZTcQypkXY3PMUePXqdAGbbC0']
        self.session = requests.session()

    # 微信消息推送
    def send_msg(self, agentId, content):
        try:
            CORP_ID = self.CORP_ID
            if agentId == 1000002:
                SECRET = self.SECRET[0]
                form_data = {
                    "touser": "LiZhaoMing",
                    "toparty": "1",
                    "totag": ["tagid1 | tagid1"],
                    "toall": 0,
                    "msgtype": "textcard",
                    "agentid": agentId,
                    "textcard": {
                        "title": "今日有新债，请申购",
                        "description": content,
                        "url": "http://data.hexin.cn/ipo/xgsgzq/",
                        "btntxt": "更多"
                    },
                    "safe": 0
                }
            if agentId == 1000003:
                SECRET = self.SECRET[1]
                form_data = {
                    "touser": "LiZhaoMing",
                    "toparty": "1",
                    "totag": ["tagid1 | tagid1"],
                    "toall": 0,
                    "msgtype": "text",
                    "agentid": agentId,
                    "text": {
                        "content": content
                    },
                    "safe": 0
                }
            if agentId == 1000004:
                SECRET = self.SECRET[2]
                form_data = {
                    "touser": "LiZhaoMing",
                    "toparty": "1",
                    "totag": ["tagid1 | tagid1"],
                    "toall": 0,
                    "msgtype": "text",
                    "agentid": agentId,
                    "text": {
                        "content": content
                    },
                    "safe": 0
                }
            url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={CORP_ID}&corpsecret={SECRET}"
            rep = self.session.get(url)
            if rep.status_code != 200:
                print("request failed.")
                return None
            token = json.loads(rep.content)['access_token']
            url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + token
            header = {
                "Content-Type": "application/json"
            }
            # 打印消息推送内容
            # print(form_data)
            r = self.session.post(url, data=json.dumps(form_data, ensure_ascii=False).encode('utf-8'), headers=header)
            if r.status_code != 200:
                print("request failed.")
            # 打印消息推送后返回的信息
            rep = json.loads(r.content)
            print(f'send success!\n{rep}')
        except Exception as rep:
            print(rep)

    # 定时推送数据
    def push_msg(self):
        # set_time = '08:50'  # 自定义提醒时间
        set_time = '8:50'  # 自定义提醒时间
        while True:
            current_time = time.strftime('%H:%M', time.localtime(time.time()))  # 获取为HH:MM时间格式
            if set_time != current_time:
                print(f'{current_time}:ready sending...')
                # pygame.mixer.init()

                readyPushMessage = ReadyPushMessage()
                # 每日简报-数据获取
                text_str, text1_str, text2_str = readyPushMessage.get_today_news()
                # 新债提醒-数据获取
                msg_bonds = readyPushMessage.get_today_bonds()
                # 每日简报-数据推送
                if text1_str and text2_str:
                    self.send_msg(1000003, text1_str)
                    time.sleep(1)
                    self.send_msg(1000003, text2_str)
                # 新债提醒-数据推送
                if msg_bonds:
                    self.send_msg(1000002, msg_bonds)
                time.sleep(60)


if __name__ == "__main__":
    # 企业微信消息推送
    wechat = WeChatPub()
    wechat.push_msg()
