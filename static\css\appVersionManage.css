/*最大宽度设置为1280px*/
@media screen and (min-width: 1280px) {
    .container {
        width: 1280px;
        padding: 0;
    }
}

h3 {
    text-align: center;
}

@media screen and (max-width: 767px) {
    .container {
        font-size: 10px;
    }

    table tr img {
        width: 30px !important;
        height: 30px !important;
    }

}

@media screen and (max-width: 415px) {
    .container {
        width: 300px;
    }
}

.container .input-group {
    margin: 10px 0;
}

.container .input-group button {
    background-color: #4279F5;
    color: white;
    /*font-weight: bolder;*/
}

table th, td {
    text-align: center;
    vertical-align: middle !important;
}

/*table th:nth-child(4), table th:nth-child(5) {*/
/*    color: #3352CC;*/
/*}*/

table td:nth-child(1) {
    color: #4279F5;
    font-weight: bolder;
}

table tr img {
    width: 40px;
    height: 40px;
}