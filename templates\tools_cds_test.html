{% include 'header.html' %}
<div class="container">
    {% if session['username'] == null %}
        <h1>欢迎使用运维助手，请先登录！</h1>
    {% else %}
        <h3>接口调试</h3>
        <p>中肿/省医EMPI接口数据获取：</p>
        <form action="/tools_cds_test" method="post">
            <div class="input-group mt-3 mb-3">
                <div class="input-group-prepend">
                    <select id="orgname" name="orgname" onchange="changeOpt()"
                            class="btn btn-secondary dropdown-toggle"
                            data-toggle="dropdown">
                        <option name="orgname">--请选择医院--</option>
                        <option name="orgname">广东省人民医院</option>
                        <option name="orgname">中山大学附属肿瘤医院</option>
                    </select>
                </div>
                <input type="text" name="patientid" class="form-control" placeholder="省医-输入患者编号 / 中肿-输入患者病例号">
                <div class="input-group-append">
                    <input type="submit" class="btn btn-primary" value="查询"></input>
                </div>
            </div>
        </form>
        {% if state =='sy_success' %}
            <div>
                <h5>省医EMPI查询结果：</h5>
                <table class="table table-striped table-hover table-bordered">
                    <tr>
                        <th>患者姓名</th>
                        <th>患者编号</th>
                        <th>年龄</th>
                        <th>性别</th>
                        <th>身份证号</th>
                        <th>手机号</th>
                    </tr>
                    {% for data in datas %}
                        <tr>
                            <td>{{ data['PATIENTNAME'] }}</td>
                            <td>{{ data['PATIENTID'] }}</td>
                            <td>{{ data['AGE'] }}</td>
                            <td>{{ data['GENDER'] }}</td>
                            <td>{{ data['IDNO'] }}</td>
                            <td>{{ data['PHONE'] }}</td>
                        </tr>
                    {% endfor %}
                </table>
            </div>
        {% elif state =='zz_success' %}
            <h5>中肿EMPI查询结果：</h5>
            <p>身份证号后四位：{{ data }}</p>
        {% elif state =='fial' %}
            <h3>查询结果</h3>
            <p>{{ datas }}</p>
        {% endif %}
    {% endif %}
</div>
{% include 'footer.html' %}