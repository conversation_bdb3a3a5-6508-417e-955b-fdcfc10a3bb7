{% include 'header.html' %}
<div class="container">
    {% if session['username'] == null %}
        <h1>欢迎使用运维助手，请先登录！</h1>
    {% else %}
        <h3>APP版本发布记录</h3>
        <div class="row">
            <input type="text" class="form-control" id="myInput" type="text" name="version_name" placeholder="输入关键字搜索">
        </div>
        <div class="row">
            <table class="table">
                <thead>
                <tr>
                    <th class="col-lg-1  col-xs-1">版本名称</th>
                    <th class="col-lg-1  col-xs-1">版本描述</th>
                    <th class="col-lg-1  col-xs-1">总下载量</th>
                    <th class="col-lg-1  col-xs-1">苹果下载量</th>
                    <th class="col-lg-1  col-xs-1">安卓下载量</th>
                    <th class="col-lg-1  col-xs-1">医院图标</th>
                    <th class="col-lg-1  col-xs-1">下载链接</th>
                    <th class="col-lg-1  col-xs-1">二维码</th>
                    <th class="col-lg-1  col-xs-1">安装指引</th>
                </tr>
                </thead>
                <tbody id="myTable">
                {% for data in result %}
                    <tr>
                        <td class="col-lg-1  col-xs-1"> {{ data['version_name'] }}</td>
                        <td class="col-lg-1  col-xs-1"> {{ data['version_desc'] }}</td>
                        <td class="col-lg-1  col-xs-1"> {{ data['all_counts'] }}</td>
                        <td class="col-lg-1  col-xs-1"> {{ data['ios_counts'] }}</td>
                        <td class="col-lg-1  col-xs-1"> {{ data['android_counts'] }}</td>
                        <td class="col-lg-1  col-xs-1">
                            <img src="{{ data['version_logo'] }}" alt="logo">
                        </td>
                        <td class="col-lg-1  col-xs-1">
                            <a href="{{ data['dowmload_url'] }}" target="_blank">
                                <img src="../static/app/link.png" alt="link">
                            </a>
                        </td>
                        <td class="col-lg-1  col-xs-1">
                            <a href="../static/app/version_imgs/{{ data['version_name'] }}.png" target="_blank">
                                <img src="../static/app/qrcode.png" alt="download">
                            </a>
                        </td>
                        <td class="col-lg-1  col-xs-1">
                            <a href="../static/app/version_imgs_new/{{ data['version_name'] }}.png" target="_blank">
                                <img src="../static/app/qrcode2.png" alt="download">
                            </a>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}
</div>

<script>
    $(document).ready(function () {
        $("#myInput").on("keyup", function () {
            var value = $(this).val().toLowerCase();
            $("#myTable tr").filter(function () {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });
    });
</script>

{% include 'footer.html' %}