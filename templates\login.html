<!doctype html>
<html lang="zh-CN">
<head>
    <!-- 必须的 meta 标签 -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link href="../static/images/favicon.ico" rel="shortcut icon">
    <!-- Bootstrap 的 CSS 文件 -->
    <link rel="stylesheet" href="/static/bootstrap/css/bootstrap.min.css">
    <!-- 引入自己的主页样式文件 -->
    <link rel="stylesheet" href="../static/css/appVersionManage.css">
    <link rel="stylesheet" href="../static/css/login.css">
    <title>运维助手</title>
</head>
<body class="text-center">
<div class="container">
    <style>
        .bd-placeholder-img {
            font-size: 1.125rem;
            text-anchor: middle;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        @media (min-width: 768px) {
            .bd-placeholder-img-lg {
                font-size: 3.5rem;
            }
        }

    </style>
    <form class="form-signin" action="/login" METHOD="post">
        <img class="mb-4" src="../static/images/favicon.ico" alt="" width="72" height="72">
        <h1 class="h3 mb-3 font-weight-normal">欢迎访问运维助手</h1>
        <label for="exampleInputusername" class="sr-only">用户名：</label>
        <input type="text" name="username" id="exampleInputusername" class="form-control" placeholder="输入用户名" required
               autofocus>
        <label for="inputPassword" class="sr-only">密码：</label>
        <input type="password" name="password" id="inputPassword" class="form-control" placeholder="输入密码" required>
        {% if emsg %}  <!-- 如果有错误信息 则显示 -->
            <div class="alert alert-danger">
                <strong>{{ emsg }}</strong>
            </div>
        {% endif %}
        <input class="btn btn-lg btn-primary btn-block" type="submit" value="登录">
        <p class="mt-5 mb-3 text-muted">&copy; 2022-2023</p>
    </form>
</div>
</body>
</html>