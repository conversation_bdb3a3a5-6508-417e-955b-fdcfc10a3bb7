{% include 'header.html' %}
{#<style>#}
{#    p{#}
{#        text-align: left;#}
{#    }#}
{#</style>#}
<div class="container">
    {% if session['username'] ==null %}
        <h1>欢迎使用运维助手，请先登录！</h1>
    {% else %}
        <h3>主机信息查询</h3>
        <p>验证码：</p>
        <form action="/get_phone_code" method="post" class="input-group" name="form">
            <input type="text" class="form-control" name="phone_code" placeholder="手机号">
            <span class="input-group-btn">
            <button class="btn btn-default" type="submit" name="search" value="查询">查询</button>
        </span>
        </form>
        <p>用户基本信息：</p>
        <form action="/get_user_info" method="post" class="input-group" name="form">
            <input type="text" class="form-control" name="phone_info" placeholder="手机号">
            <span class="input-group-btn">
            <button class="btn btn-default" type="submit" name="search" value="查询">查询</button>
        </span>
        </form>
        <p>用户关联信息：</p>
        <form action="/get_user_bind_info" method="post" class="input-group" name="form">
            <input type="text" class="form-control" name="phone_band" placeholder="手机号/数据账号">
            <span class="input-group-btn">
            <button class="btn btn-default" type="submit" name="search" value="查询">查询</button>
        </span>
        </form>
        <p>账号操作记录：</p>
        <form action="/get_operation_record" method="post" class="input-group" name="form">
            <input type="text" class="form-control" name="data_account" placeholder="数据账号/UserID">
            <span class="input-group-btn">
            <button class="btn btn-default" type="submit" name="search" value="查询">查询</button>
        </span>
        </form>
        <p>医院信息：</p>
        <form action="/get_hospital_info" method="post" class="input-group" name="form">
            <input type="text" class="form-control" name="org_name" placeholder="医院名称关键字">
            <span class="input-group-btn">
            <button class="btn btn-default" type="submit" name="search" value="查询">查询</button>
        </span>
        </form>
        <p>解锁账号：</p>
        <form action="/unlock_login" method="post" class="input-group" name="form">
            <input type="text" class="form-control" name="lock_phone" placeholder="手机号">
            <span class="input-group-btn">
            <button class="btn btn-default" type="submit" name="unlock" value="解锁">解锁</button>
        </span>
        </form>
        <p>设备登录记录：</p>
        <form action="/del_login_record" method="post" class="input-group" name="form">
            <input type="text" class="form-control" name="user_id" placeholder="UserID">
            <span class="input-group-btn">
            <button class="btn btn-default" type="submit" name="search" value="删除">删除</button>
        </span>
        </form>
        <p>删除会诊记录、解散会诊群组(近10条会诊以前的记录)：</p>
        <form action="/del_consultation_record" method="post" class="input-group" name="form">
            <input type="text" class="form-control" name="conn_user_id" placeholder="UserID">
            <span class="input-group-btn">
            <button class="btn btn-default" type="submit" name="search" value="删除">删除</button>
        </span>
        </form>
    {% endif %}
</div>
{% include 'footer.html' %}