{% include 'header.html' %}
<div class="container">
    {% if session['username'] == null %}
        <h1>欢迎使用运维助手，请先登录！</h1>
    {% else %}
        <div class="tab-content">
            <h3>注册工具（CDS/大屏/引擎）</h3>
            <form action="/product_reg" method="post">
                <p for="reg_info">注册信息：申请人-城市-项目名称(医院名/本机)-产品名称(CDS/PC/Engine)</p>
                <div class="input-group">
                    <input type="text" class="form-control" name="reg_info" value="{{ reg_info }}"
                           placeholder="例:李某某-广州-安泰云医院-CDS">
                </div>
                <p for="mac_code">机器码：</p>
                <div class="input-group">
                    <input type="text" class="form-control" name="mac_code" value="{{ mac_code }}">
                    <input class="btn btn-primary" type="submit" name="reg_cds" value="生成注册码">
                </div>
            </form>
            <p for="reg_code">注册码：</p>
            <div class="input-group">
                <input id="reg_code" type="text" class="form-control" name="reg_code" value="{{ reg_code }}">
                <input class="btn btn-primary" type="button" name="copy_text" value="复制注册码"
                       onclick="copy_text('reg_code')">
            </div>
        </div>
        </div>
    {% endif %}
</div>
{% include 'footer.html' %}