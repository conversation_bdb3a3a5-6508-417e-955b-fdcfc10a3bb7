import json
import time
import requests
import random

'''
实现会诊记录删除、腾讯群组解散
1、获取会诊id和群组id
2、删除会诊记录，解散群组
'''


class ConsultationManager:
    def __init__(self):
        pass

    # 获取腾讯云管理用户签名
    def get_sign(self):
        get_sign_url = 'http://app.51mdt.cn:8080/v1/getSignature/ANNETPUSH'
        r = requests.get(get_sign_url)
        usersign = r.json()['data']['UserSig']
        return usersign

    # 生成n位随机验证码
    def rand_verify_code(self, n=32):
        nums = ""
        for i in range(n):
            rand_num = str(random.randint(0, 9))
            nums += rand_num
        return nums

    # 查询会诊ID和群组ID
    def get_consultation_groupid(self, userid, num):
        get_consultation_url = f'http://app.51mdt.cn:8080/v1/consultation/getConsultation/{userid}/create/1/{num}'
        r = requests.get(get_consultation_url)
        # print(r.json())
        datas = json.loads(r.content.decode())
        ids = []
        for data in datas['data']:
            ids.append({
                'consultationId': data.get('consultationId', ''),
                'sessionId': data.get('sessionId', '')
            })
        ids = sorted(ids, key=lambda x: x['consultationId'], reverse=False)
        return ids

        # # 查询腾讯云群组
        # def get_tencent_group(self):
        #     usersign = self.get_sign()
        #     nums = self.rand_verify_code(32)
        #     get_group_url = f'https://console.tim.qq.com/v4/group_open_http_svc/get_joined_group_list?sdkappid=**********&identifier=ANNETPUSH&usersig={usersign}&random={nums}&contenttype=json'
        #     data = {
        #         "Member_Account": "101821"
        #     }
        #     data_json = json.dumps(data, ensure_ascii=False)
        #     resp = requests.post(get_group_url, data=data_json)
        #     groupid_list = resp.json()['GroupIdList']
        #     # print(len(groupid_list))
        #     groupids = []
        #     for groupid in groupid_list:
        #         groupids.append(groupid['GroupId'])
        #     print(len(groupids), groupids)
        #     return groupids

    # 删除会诊表记录（从接口获取consultationId）
    def del_table_conn_record(self, consultationId):
        try:
            destroy_table_url = 'http://app.51mdt.cn:8080/v1/consultation/deleteConsultation'
            headers = {
                "Content-Type": "application/json;charset=UTF-8"
            }
            data = {
                'consultationId': consultationId
            }
            data_json = json.dumps(data, ensure_ascii=False)
            resp = requests.post(destroy_table_url, headers=headers, data=data_json)
            if resp.status_code != 200:
                print(f'{resp.status_code},会诊{consultationId}删除成功')
            print(f'{resp.status_code},会诊{consultationId}删除成功')
        except Exception as e:
            print(e)

    # 解散群组（从接口获取groupid）
    def destroy_group(self, sessionId):
        try:
            usersign = self.get_sign()
            nums = self.rand_verify_code(32)
            destroy_group_url = f'https://console.tim.qq.com/v4/group_open_http_svc/destroy_group?sdkappid=**********&identifier=ANNETPUSH&usersig={usersign}&random={nums}&contenttype=json'
            data = {
                'GroupId': sessionId
            }
            data_json = json.dumps(data, ensure_ascii=False)
            resp = requests.post(destroy_group_url, data=data_json)
            if resp.status_code != 200:
                print(f'{resp.status_code},群组{sessionId}解散失败')
            print(f'{resp.status_code},群组{sessionId}解散成功')
        except Exception as e:
            print(e)


if __name__ == "__main__":
    consultationManager = ConsultationManager()  # 实例化会诊管理类
    ids = consultationManager.get_consultation_groupid(108755, 500)  # 查询会诊中心前多少条会诊群组ID
    # print(ids)
    for id in ids[:-5]:
        consultationId = id.get('consultationId')
        sessionId = id.get('sessionId')
        print(consultationId)
        # consultationManager.del_table_conn_record(consultationId)  # 删除会诊表记录
        # consultationManager.destroy_group(sessionId)  # 解散群组
        # time.sleep(1)
