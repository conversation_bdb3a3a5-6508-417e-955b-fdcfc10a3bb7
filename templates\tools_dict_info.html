{% include 'header.html' %}
{#<style>#}
{#    @media screen and (min-width: 1280px) {#}
{#        .container {#}
{#            width: 1280px;#}
{#            padding: 0;#}
{#        }#}
{#    }#}
{##}
{#    h3, table th {#}
{#        text-align: center;#}
{#    }#}
{#</style>#}
<div class="container">
    {% if session['username'] == null %}
        <h1>欢迎使用运维助手，请先登录！</h1>
    {% else %}
        <h3>字典信息查看</h3>
        <ul class="nav nav-tabs" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" data-toggle="tab" href="#errcode">CDS错误码 </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-toggle="tab" href="#message">CDS消息报文 </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-toggle="tab" href="#pathtype">CDS图像路径 </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-toggle="tab" href="#icstatus">IC报告影像状态 </a>
            </li>
        </ul>
        <div class="tab-content">
            <div id="errcode" class="container tab-pane active">
                <table class="table table-striped table-hover table-bordered">
                    <tr>
                        <th>编码</th>
                        <th>交互标识</th>
                        <th>含义</th>
                        <th>场景</th>
                    </tr>
                    {% for data in errcode_result %}
                        <tr>
                            <td>{{ data['num'] }}</td>
                            <td>{{ data['code'] }}</td>
                            <td>{{ data['explain'] }}</td>
                            <td>{{ data['mark'] }}</td>
                        </tr>
                    {% endfor %}
                </table>
            </div>
            <div id="message" class="container tab-pane fade">
                <table class="table table-striped table-hover table-bordered">
                    <tr>
                        <th>编码</th>
                        <th>报文号</th>
                        <th>含义</th>
                    </tr>
                    {% for data in message_result %}
                        <tr>
                            <td>{{ data['message_num'] }}</td>
                            <td>{{ data['message_id'] }}</td>
                            <td>{{ data['message_name'] }}</td>
                        </tr>
                    {% endfor %}
                </table>
            </div>
            <div id="pathtype" class="container tab-pane fade">
                <table class="table table-striped table-hover table-bordered">
                    <tr>
                        <th>路径类型</th>
                        <th>路径说明</th>
                        <th>使用场景</th>
                    </tr>
                    {% for data in pathtype_result %}
                        <tr>
                            <td>{{ data['pathtype_num'] }}</td>
                            <td>{{ data['pathtype_name'] }}</td>
                            <td>{{ data['pathtype_mark'] }}</td>
                        </tr>
                    {% endfor %}
                </table>
            </div>
            <div id="icstatus" class="container tab-pane fade">
                <table class="table table-striped table-hover table-bordered">
                    <tr>
                        <th>编码</th>
                        <th>报告状态</th>
                        <th>含义</th>
                    </tr>
                    {% for data in ic_status_result[1:9] %}
                        <tr>
                            <td>{{ data['status_num'] }}</td>
                            <td>{{ data['status_id'] }}</td>
                            <td>{{ data['status_mark'] }}</td>
                        </tr>
                    {% endfor %}
                </table>
                <table class="table table-striped table-hover table-bordered">
                    <tr>
                        <th>编码</th>
                        <th>影像状态</th>
                        <th>含义</th>
                    </tr>
                    {% for data in ic_status_result[10:] %}
                        <tr>
                            <td>{{ data['status_num'] }}</td>
                            <td>{{ data['status_id'] }}</td>
                            <td>{{ data['status_mark'] }}</td>
                        </tr>
                    {% endfor %}
                </table>
            </div>
        </div>
    {% endif %}
</div>
{% include 'footer.html' %}