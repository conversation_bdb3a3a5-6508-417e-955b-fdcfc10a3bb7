# 注册CDS、大屏、引擎工具
# 机器码：hv9kvgkgj8FE vgeFc5hv
# 注册码：9k6khgkgj8FE gkgvk9vh vgeFc5hv
import base64
import datetime
import hashlib
import time
import urllib.parse
import json
import requests


class MyTools:
    def __init__(self):
        pass

    # CDS/大屏/引擎注册
    def register_cds(self, str):
        dict_Jiemi = {'k': '0', 'v': '1', 'g': '2', 'j': '3', 'h': '4', 'c': 'A', 'd': 'B', 'e': 'C'};
        dict_Jiami = {'0': 'k', '1': 'v', '2': 'g', '3': 'j', '4': 'h', 'A': 'c', 'B': 'd', 'C': 'e'};

        # 12345678abcd ABCDEFGH
        # 加天数后的时间加密再翻转abcd 87654321 ABCDEFGH
        # 原始
        if (21 == len(str)):
            register_Date = str[0:8]
            # 解密时间
            mingwen_Date = ''
            for i in register_Date:
                if (i in dict_Jiemi):
                    temp = dict_Jiemi[i]
                else:
                    temp = i
                mingwen_Date = temp + mingwen_Date
            # 计算有效时间
            expiry_Date_Tuple = datetime.datetime.strptime(mingwen_Date, "%Y%m%d") + datetime.timedelta(
                days=36)  # 时间元组格式
            expiry_Date = datetime.datetime.strftime(expiry_Date_Tuple, "%Y%m%d")
            # 有效时间加密
            miwen_Date = ''
            for i in expiry_Date:
                if (i in dict_Jiami):
                    temp = dict_Jiami[i]
                else:
                    temp = i
                miwen_Date = temp + miwen_Date
            # 拼成加密字符串
            register_Date_Reverse = register_Date[::-1]
            key = miwen_Date + str[8:13] + register_Date_Reverse + str[-9:]
        else:
            key = '注册码格式不正确，请重新输入...'
        return key

    # URL编解码
    def process_text(self, type, text):
        try:
            if type == 'bs4encode':
                bytes_str = text.encode("utf-8")
                result = base64.b64encode(bytes_str).decode("utf-8")  # 被编码的参数必须是二进制数据
            elif type == 'bs4decode':
                result = base64.b64decode(text).decode("utf-8")
            elif type == 'urlencode':
                result = urllib.parse.quote(text)
            elif type == 'urldecode':
                result = urllib.parse.unquote(text)
            elif type == 'str2ascii':
                result = text.encode('unicode_escape').decode('ascii')
            elif type == 'ascii2str':
                result = text.encode('ascii').decode('unicode_escape')
            elif type == 'jsonformat':
                try:
                    result = json.dumps(json.loads(text), ensure_ascii=False, indent=4)
                except Exception as e:
                    result = f'JSON字符串格式错误{e}'
            elif type == 'md5':
                hl = hashlib.md5()
                # 对字符串加密，并转为大写
                hl.update(text.encode(encoding='utf-8'))
                result = hl.hexdigest().upper()
            elif type == 'translate':
                try:
                    # API
                    url = 'http://fanyi.youdao.com/translate?smartresult=dict&smartresult=rule&smartresult=ugc&sessionFrom=null'
                    # 传输的参数， i为要翻译的内容
                    key = {
                        'type': "AUTO",
                        'i': text,
                        "doctype": "json",
                        "version": "2.1",
                        "keyfrom": "fanyi.web",
                        "ue": "UTF-8",
                        "action": "FY_BY_CLICKBUTTON",
                        "typoResult": "true"
                    }
                    # key 这个字典为发送给有道词典服务器的内容
                    response = requests.post(url, data=key)
                    # 判断服务器是否相应成功
                    if response.status_code == 200:
                        # 通过 json.loads 把返回的结果加载成 json 格式
                        datas = json.loads(response.text)
                        #         print ("输入的词为：%s" % result['translateResult'][0][0]['src'])
                        #         print ("翻译结果为：%s" % result['translateResult'][0][0]['tgt'])
                        # print(datas['translateResult'])
                        result = ''
                        for data in datas['translateResult'][0:]:
                            for translateResult in data:
                                result += translateResult['tgt'] + '\n'
                except Exception as e:
                    print("有道词典调用失败")
                    # 相应失败就返回空
                    result = f'翻译失败:{e}'
            elif type == 'time_timestamp':
                if len(text) == 10 or len(text) == 13:
                    try:
                        text = int(text[:10])
                        time_lcoal = time.localtime(text)
                        result = time.strftime('%Y-%m-%d %H:%M:%S', time_lcoal)
                    except:
                        result = f'字符串格式错误:\n时间戳转日期，格式如：1664260942\n日期转时间戳，格式如：2016-05-05 20:28:54'
                else:
                    try:
                        time_array = time.strptime(text, '%Y-%m-%d %H:%M:%S')
                        result = int(time.mktime(time_array))
                    except:
                        result = f'字符串格式错误:\n时间戳转日期，格式如：1664260942\n日期转时间戳，格式如：2016-05-05 20:28:54'
            return result
        except Exception as result:
            result = f'字符串格式错误:{result}'
            return result

    # base64解码生成pdf
    def bs64_decrypt_pdf(self, text):
        timestamp = int(time.time())
        name = 'emr_' + str(timestamp)
        filepath = f'./static/pdf/{name}.pdf'
        with open(filepath, 'wb') as f:
            f.write(base64.b64decode(text))
            return filepath

    # bs64_decrypt_pdf('data:application/pdf;base64,JVBERi0xLjcNCiW1tbW1DQoxIDAgb2JqDQo8PC9UeXBlL0NhdGFsb2cvUGFnZXMgMiAwIFIvTGFuZyh6aC1DTikgL1N')


if __name__ == '__main__':
    myTools = MyTools()
    print(myTools.register_cds('9g9kggkgj8FE vgeFc5hv'))
    # print(myTools.process_text('bs4encode', '123456'))
    # print(myTools.process_text('bs4decode', 'MTIzNDU2'))
    # print(myTools.process_text('urlencode', '广州'))
    # print(myTools.process_text('urldecode', '%E5%B9%BF%E5%B7%9E'))
    # print(myTools.process_text('str2ascii', 'MDT权限管理系统'))
    # print(myTools.process_text('ascii2str', r'\u5b89\u6cf0'))
    # print(myTools.process_text('md5', 'yxm'))
    # print(myTools.process_text('translate', '''今天，人民日报发表作者为“仲音”的评论员文章——《'''))
    # print(myTools.process_text('timestamp2time', '1664260942'))
    # print(myTools.process_text('time2timestamp', '2022-09-27 14:42:22'))