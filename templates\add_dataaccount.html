{% include 'header.html' %}
<div class="container">
    {% if session['username'] == null %}
        <h1>欢迎使用运维助手，请先登录！</h1>
    {% else %}
        <h3>演示医院账号添加</h3>
        <p>选择医院和所属科室，输入账号姓名，默认分配的权限科室为重症医学科住院(演示)，账号为姓名对应的拼音...</p>
        <form action="/add_dataaccount" method="post">
            <div class="input-group mt-3 mb-3">
                <div class="input-group-prepend">
                    <select id="orgname" name="orgname" onchange="changeOpt()"
                            class="btn btn-secondary dropdown-toggle"
                            data-toggle="dropdown">
                        <option name="orgname">--请选择医院--</option>
                        <option name="orgname">安泰云医院</option>
                        <option name="orgname">安友云医院</option>
                        <option name="orgname">亚新云医院</option>
                    </select>
                </div>
                <div class="input-group-prepend">
                    <select id="deptname" name="deptname" class="btn btn-secondary dropdown-toggle"
                            data-toggle="dropdown">
                        <option name="deptname">--请选择所属科室--</option>
                    </select>
                </div>
                <input type="text" name="name" class="form-control" placeholder="输入姓名">
                <div class="input-group-append">
                    <button type="submit" class="btn btn-primary" value="添加">添加</button>
                </div>
            </div>
        </form>
        {% if code ==0 %}
            <div class="alert alert-success">
                <strong>添加成功！</strong>
            </div>
            <div class="input-group mb-3">
                <input type="text" id="result" class="form-control" value={{ result }}>
                <div class="input-group-append">
                    <input class="btn btn-primary" type="submit" value="复制" onclick="copy_text('result')"></input>
                </div>
            </div>
        {% elif code !=0 and code !=null %}
            <div class="alert alert-danger">
                <strong>添加失败！错误信息：{{ error }}，错误码：{{ code }}</strong>
            </div>{% endif %}
        </div>
        <script>
            function changeOpt() {
                //创建一个数组
                var array = new Array()
                //以二维数组将数组里的省份里分别放入对应省份的城市
                array['--请选择医院--'] = ['--请选择所属科室--']
                array['安泰云医院'] = ['产品部', '运维部', '销售部', '市场部', '测试']
                array['安友云医院'] = ['技术', '来宾', '演示', '重症医学科住院(演示)']
                array['亚新云医院'] = ['重症医学科住院(演示)', '装机和运维']
                //获取第一个下拉列表的内容
                var orgname = document.getElementById("orgname").value
                //定义变量str用于后面接收数据
                var str = ""
                for (var i = 0; i < array[orgname].length; i++) {
                    //获取每次循环对应省份的二维数组的元素
                    str += "<option name=" + "deptname" + " value='" + array[orgname][i] + "'>" + array[orgname][i] + "</option>"
                    //将每次获取到的二维数组元素放入下拉列表中
                    document.getElementById("deptname").innerHTML = str
                }
            }
        </script>
    {% endif %}
</div>
{% include 'footer.html' %}