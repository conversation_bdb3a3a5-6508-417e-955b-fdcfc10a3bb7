/*最大宽度设置为1280px*/
@media screen and (min-width: 1280px) {
    .container {
        width: 1280px;
    }
}

body {
    background-color: #8c99b9;
    background-image: linear-gradient(135deg, #8c99b9 100%, #aab7ca 100%);
}

ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

/*a {*/
/*    color: #ffffff;*/
/*    text-decoration: none;*/
/*}*/
h1 {
    text-align: center;
    margin-top: 80px !important;
    font-family: "Microsoft YaHei UI Light";
    font-weight: bolder;
    color: #FDFDFD !important;
}

a:hover, a:visited, a:active {
    text-decoration: none;
    color: #d58512;
    font-weight: bolder;
}


.row {
    margin: 180px auto;
}

ul li {
    float: left;
    padding-right: 10px !important;
}

ul a img {
    display: block;
    width: 50%;
    margin: 0 auto;
}

ul a span {
    display: block;
    text-align: center !important;
    padding: 20px 0;
    font-size: 18px;
    color: #eeeeef;
}
